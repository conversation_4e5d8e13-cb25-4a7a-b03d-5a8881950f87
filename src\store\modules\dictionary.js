/*
 * @Author: wang<PERSON> <EMAIL>
 * @Date: 2024-05-13 09:42:02
 * @LastEditors: lugege <EMAIL>
 * @LastEditTime: 2025-04-24 17:10:43
 * @FilePath: \bigscreen-qj-web\src\store\modules\dictionary.js
 * @Description:
 *
 */
// import {  commonalityApi } from '@JT/api'
// const {getDictMap,getServerUrl} = commonalityApi
import lib from '@/utils/lib.ts'
import { defineStore } from 'pinia'

const useDictionary = defineStore('dictionary', {
  state: () => ({
    fileUrl: 'http://***********:10000/stec-platform-doc/', //文件前缀url
    // dealDictMap:{},
    dictMap: [],
    // #region 大图预览
    showBigImgList: []
    // #endregion 大图预览
  }),
  getters: {
    // fileUrlGetter: (state) => state.fileUrl,
    // dictMapGetter: (state) => state.dictMap
  },
  actions: {
    // #region 大图预览
    DIALOG_IMG(params) {
      const state = this
      state.showBigImgList = params
    },
    // #endregion 大图预览
    setDictMap() {
      // 获取字典
      return new Promise((resolve, reject) => {
        lib.api.dicDiyApi.list({}).then((response) => {
          if (response.success) {
            const state = this
            state.dictMap = response.result
            resolve(response.result)
          }
        })
        // getDictMap().then((response) => {
        //   if (response.success) {
        //     const dealDictMap = {}
        //     Object.entries(response.result).forEach((item) => {
        //       const value = {}
        //       item[1].forEach((it) => {
        //         value[it.code] = it.name
        //       })
        //       dealDictMap[item[0]] = value
        //     })
        //     this.dealDictMap = dealDictMap
        //     this.dictMap = response.result
        //     resolve(response.result)
        //   }
        // })
      })
    }
    // setFileUrl() {//获取文件前缀
    //   getServerUrl().then(response => {
    //     if (response.success) {
    //       this.fileUrl = response.result
    //     }
    //   })
    // },
  }
})

export default useDictionary
