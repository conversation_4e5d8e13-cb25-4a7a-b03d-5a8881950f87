<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import AIChat from '@/components/AIChat/index.vue'
import ConversationList from '@/components/AIChat/components/ConversationList/index.vue'
import { useAiChat, useAppState } from '@/components/AIChat/helper'
import { ElButton } from 'element-plus'
import { Menu, Close } from '@element-plus/icons-vue'

const showChat = ref(true)
const sidebarCollapsed = ref(true)
const showSidebarModal = ref(false)

const {
  conversationList,
  currentConversationId,
  handleCoversationIdChange,
  handleConversationRename,
  handleConversationDelete,
  handleNewConversation,
  getConversationList
} = useAiChat()

// 添加 watch 来监听 conversationList 的变化
watch(conversationList, (newVal) => {
  console.log('conversationList changed --- :', newVal)
}, { deep: true })

// 使用新的应用状态管理
const { currentAppKey, currentAppKeyValue } = useAppState()

const conversationListLoading = ref(false)

const handleCloseChat = () => {
  showChat.value = false
}

// 处理对话切换
const handleConversationChange = async (conversationId: string) => {
  await handleCoversationIdChange(conversationId)
  // 如果是弹出模式，切换后关闭弹出层
  if (showSidebarModal.value) {
    showSidebarModal.value = false
  }
}

// 切换侧边栏状态
const toggleSidebar = () => {
  if (sidebarCollapsed.value) {
    // 如果是收起状态，显示弹出层
    showSidebarModal.value = true
  } else {
    // 如果是展开状态，收起侧边栏
    sidebarCollapsed.value = true
  }
}

// 关闭弹出层
const closeSidebarModal = () => {
  showSidebarModal.value = false
}

// 展开侧边栏
const expandSidebar = () => {
  sidebarCollapsed.value = false
  showSidebarModal.value = false
}

// 初始化时获取对话列表
onMounted(async () => {
  conversationListLoading.value = true
  try {
    await getConversationList()
  } finally {
    conversationListLoading.value = false
  }
})
</script>

<template>
  <div class="app-container">
    <div class="chat-layout">
      <!-- 侧边栏 -->
      <div 
        class="sidebar" 
        :class="{ 'collapsed': sidebarCollapsed }"
        v-if="!sidebarCollapsed"
      >
        <div class="sidebar-header">
          <h3>AI Chat Demo</h3>
          <div class="app-info">
            <el-text type="info" size="small">
              当前应用: {{ currentAppKey }}
            </el-text>
          </div>
          <el-button 
            :icon="Close" 
            size="small" 
            text 
            @click="toggleSidebar"
            class="collapse-btn"
          />
        </div>
        <div class="sidebar-content">
          <ConversationList
            :conversation-list="conversationList"
            :active-conversation-id="currentConversationId"
            :loading="conversationListLoading"
            @conversation-change="handleConversationChange"
            @conversation-rename="handleConversationRename"
            @conversation-delete="handleConversationDelete"
            @new-conversation="handleNewConversation"
          />
        </div>
      </div>

      <!-- 收起状态的触发按钮 -->
      <!-- <div v-if="sidebarCollapsed" class="sidebar-trigger">
        <el-button
          :icon="Menu"
          type="primary"
          circle
          @click="toggleSidebar"
          class="expand-btn"
        />
      </div> -->

      <!-- 主要内容区域 -->
      <div class="main-content" :class="{ 'full-width': sidebarCollapsed }">
        <AIChat @close="handleCloseChat" />
      </div>
    </div>

    <!-- 弹出层模态框 -->
    <div v-if="showSidebarModal" class="sidebar-modal-overlay" @click="closeSidebarModal">
      <div class="sidebar-modal" @click.stop>
        <div class="sidebar-modal-header">
          <div class="modal-title-section">
            <h3>对话列表</h3>
            <el-text type="info" size="small">
              当前应用: {{ currentAppKey }}
            </el-text>
          </div>
          <div class="header-actions">
            <el-button 
              size="small" 
              @click="expandSidebar"
              type="primary"
              text
            >
              固定展开
            </el-button>
            <el-button 
              :icon="Close" 
              size="small" 
              text 
              @click="closeSidebarModal"
            />
          </div>
        </div>
        <div class="sidebar-modal-content">
          <ConversationList
            :conversation-list="conversationList"
            :active-conversation-id="currentConversationId"
            :loading="conversationListLoading"
            @conversation-change="handleConversationChange"
            @conversation-rename="handleConversationRename"
            @conversation-delete="handleConversationDelete"
            @new-conversation="handleNewConversation"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;

  /* background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); */
}
.chat-layout {
  position: relative;
  display: flex;
  height: 100%;
}

/* 侧边栏样式 */
.sidebar {
  z-index: 100;
  display: flex;
  flex-direction: column;
  width: 320px;
  height: 100%;
  background: rgb(255 255 255 / 95%);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgb(255 255 255 / 30%);
  transition: all 0.3s ease;
}
.sidebar.collapsed {
  width: 0;
  overflow: hidden;
}
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgb(255 255 255 / 80%);
  border-bottom: 1px solid #e4e7ed;
}
.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}
.app-info {
  margin-bottom: 4px;
}
.sidebar-content {
  flex: 1;
  overflow: hidden;
}

/* 收起状态的触发按钮 */
.sidebar-trigger {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 200;
}
.expand-btn {
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
}

/* 主要内容区域 */
.main-content {
  position: relative;
  flex: 1;
  height: 100%;
  transition: all 0.3s ease;
}
.main-content.full-width {
  width: 100%;
}
.main-content > div {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 30%);

  /* background: rgba(255, 255, 255, 0.2); */
  border-radius: 0 12px 12px 0;
}
.main-content.full-width > div {
  border-radius: 12px;

  /* margin: 10px;
  height: calc(100% - 20px);
  width: calc(100% - 20px); */
}

/* 弹出层模态框样式 */
.sidebar-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background: rgb(0 0 0 / 50%);
  backdrop-filter: blur(4px);
}
.sidebar-modal {
  display: flex;
  flex-direction: column;
  width: 350px;
  height: 100%;
  background: rgb(255 255 255 / 95%);
  backdrop-filter: blur(20px);
  border-radius: 0 12px 12px 0;
  box-shadow: 4px 0 20px rgb(0 0 0 / 15%);
  animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.sidebar-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgb(255 255 255 / 80%);
  border-bottom: 1px solid #e4e7ed;
}
.modal-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.modal-title-section h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}
.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}
.sidebar-modal-content {
  flex: 1;
  overflow: hidden;
}

/* 按钮样式优化 */
.collapse-btn, .expand-btn {
  transition: all 0.2s ease;
}
.collapse-btn:hover, .expand-btn:hover {
  transform: scale(1.1);
}

/* 深度选择器，用于修改子组件样式 */
:deep(.conversation-list-container) {
  height: 100%;
  .conversations-container {
    width: 100%;
    height: 100%;
    .conversations-list {
      width: 100% !important;
      height: calc(100% - 60px);
    }
  }
}
</style>