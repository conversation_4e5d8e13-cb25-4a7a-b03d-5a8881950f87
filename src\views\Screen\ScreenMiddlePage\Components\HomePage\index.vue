<template>
  <div class="homepage-container">
    <div id="map-container"></div>
  </div>
</template>

<script setup lang="ts">
  import { mapHomeStyle } from '@/utils/mapStyle'
  import lib from '@/utils/lib'
  onMounted(() => {
    const map = new BMapGL.Map('map-container', {
      minZoom: 7,
      enableTilt: true,
      displayOptions: {
        // 在初始化时就设置displayOptions
        poiIcon: false,
        building: false,
        indoor: false,
        skyColors: false // 和这个
      }
    })
    map.centerAndZoom(new BMapGL.Point(121.575397946, 31.2808169638), 12)
    map.enableScrollWheelZoom() // 启用滚轮
    map.setMapStyleV2({
      styleJson: mapHomeStyle
    })

    lib.bus.busMap.on((isShowShangHai: boolean) => {
      console.log('isShowShangHai', isShowShangHai)
      if(isShowShangHai){
        map.flyTo(new BMapGL.Point(121.575397946, 31.2808169638), 12)
      } else {
        map.flyTo(new BMapGL.Point(119.52951,29.677855), 10)
      }
    })

    onUnmounted(() => {
      lib.bus.busMap.reset()
    })
  })
</script>

<style lang="scss" scoped>
  .homepage-container {
    width: 3120px;
    height: 1440px;
    #map-container {
      width: 3120px;
      height: 1440px;
    }
  }
</style>
