import { useXStream } from 'vue-element-plus-x'
import type { BubbleListItemProps, BubbleListProps } from 'vue-element-plus-x/bubbleList/types'
// import mdTable from 'markdown-it-multimd-table'
// import MarkdownIt from 'markdown-it'
import lib from '@/utils/lib'

export const useAiChat = (appKey) => {
  const inputValue = ref('')
  const recording = ref(false)
  const submitLoading = ref(false)
  const currentConversationId = ref('')
  const showSender = ref(false)
  const isCarryHistory = ref(false)

  // const tablePlugin = new MarkdownIt({
  //   html: true, // 允许 HTML 标签
  //   linkify: true // 自动转换链接
  // }).use(mdTable, {
  //   multiline: true, // 启用多行表格语法
  //   rowspan: true // 支持跨行单元格
  // })
  // const mdPlugins = [tablePlugin]

  const key = ref(0)
  type listType = BubbleListItemProps & {
    key: number
    role: 'user' | 'ai'
    thinkingContent?: string //思考内容
    thinkingStatus?: 'start' | 'thinking' | 'end' | 'error' //思考状态
    quoteList?: any[] //引用列表
    echartsJSONData?: any //echartsJSONData
  }
  const list: BubbleListProps<listType>['list'] = ref([])

  const { startStream, cancel, data, error, isLoading } = useXStream()

  /**录音 */
  const handleRecord = () => {
    recording.value = !recording.value
    console.log('正在录音', recording.value)
  }

  /**取消提交 */
  const handleCancel = () => {
    if (submitLoading.value) {
      cancel()
      list.value = list.value.slice(0, list.value.length - 2)
      submitLoading.value = false
    }
  }

  /** 清空 */
  const handleClear = () => {
    console.log('handleClear')
    list.value = []
    currentConversationId.value = ''
    inputValue.value = ''
    submitLoading.value = false
    cancel()
  }

  const handleThinkingChange = (payload: { value: boolean; status: 'start' | 'thinking' | 'end' | 'error' }) => {
    console.log('value', payload.value, 'status', payload.status)
  }

  const handleSubmit = () => {
    console.log('提交')
    submitLoading.value = true

    key.value++
    list.value.push({
      key: key.value, // 唯一标识
      role: 'user', // user | ai 自行更据模型定义
      placement: 'end', // start | end 气泡位置
      content: inputValue.value // 消息内容 流式接受的时候，只需要改这个值即可
    })

    key.value++
    const newReply: BubbleListProps<listType> = {
      key: key.value, // 唯一标识
      role: 'ai', // user | ai 自行更据模型定义
      placement: 'start', // start | end 气泡位置
      content: '', // 消息内容 流式接受的时候，只需要改这个值即可
      loading: true, // 当前气泡的加载状态
      shape: 'corner', // 气泡的形状
      variant: 'filled', // 气泡的样式
      isMarkdown: true, // 是否渲染为 markdown
      // mdPlugins, // 自定义 markdown 插件
      typing: true, // 是否开启打字器效果 该属性不会和流式接受冲突
      isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
      maxWidth: '1700px',
      thinkingContent: '',
      thinkingStatus: 'thinking'
    }

    list.value.push(newReply)

    startSSE(inputValue.value)
  }

  const startSSE = async (query) => {
    if (isCarryHistory.value) {
      query = historyStr.value + '\n\n\n\n' + query
      isCarryHistory.value = false
    }
    try {
      const response = await fetch(`${window.config.difyAPIBaseUrl}/chat-messages`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'text/event-stream' },
        body: JSON.stringify({
          // app_key: window.config.appKey,
          app_key: appKey.value,
          query: query,
          response_mode: 'streaming',
          inputs: {},
          conversation_id: currentConversationId.value
        })
      })
      const readableStream = response.body!
      inputValue.value = ''

      await startStream({ readableStream })
      submitLoading.value = false

      //结束后保存会话信息
      // if (currentConversationId.value === '') {
      getConversationIdFromServer()
      // }
    } catch (err) {
      console.error('Fetch error:', err)
    }
  }

  /**获取当前会话的conversation_id */
  const getConversationIdFromServer = async () => {
    const chatClient = new window.DifyChatboxSDK.ChatClientWeb(window.config.difyAPIBaseUrl, appKey.value)

    const conversationList = await chatClient.getConversations()
    console.log(conversationList)

    // 上面创建的新的聊天 在 conversationList.data[0]
    if (conversationList && conversationList.data.length > 0) {
      const conversation = conversationList.data[0]
      console.log(conversation)
      currentConversationId.value = conversation.id
    }
  }

  const historyStr = computed(() => {
    const aiHistory = list.value.filter((item) => item.content !== '').slice(-10, -1)
    let num = 1

    return aiHistory
      .map((item, index) => {
        if (item.role === 'user') {
          return `历史提问${num}/5  ${item.content}`
        } else {
          num++
          return `历史回答${num - 1}/5  ${item.content}`
        }
      })
      .join('\n\n')
  })

  const content = computed(() => {
    if (!data.value.length) return ''
    let text = ''
    for (let index = 0; index < data.value.length; index++) {
      const chunk = data.value[index].data
      if (chunk) {
        try {
          const parsedChunk = JSON.parse(chunk)
          if (parsedChunk.event === 'message') {
            text += parsedChunk.answer
          } else if (parsedChunk.event === 'error') {
            return 'error'
          }
        } catch (error) {
          console.error('解析数据时出错:', error)
        }
      }
    }
    return text
  })
  watch(content, (newVal) => {
    if (newVal) {
      console.log('newVal-------', newVal)
      const currentReply = list.value.find((_) => _.key === key.value)
      if (newVal === 'error') {
        currentReply.thinkingStatus = 'error'
        currentReply.loading = false
        return
      }
      if (newVal.includes('<think>')) {
        if (newVal.includes('</think>')) {
          currentReply.thinkingStatus = 'end'
          currentReply.thinkingContent = newVal.slice(newVal.indexOf('<think>') + 7, newVal.indexOf('</think>'))
          const noThink = newVal.slice(newVal.indexOf('</think>') + 8)
          console.log('noThink-------', noThink)
          currentReply.content = noThink.slice(0, noThink.indexOf('echartsJSONData:'))
          currentReply.loading = false
        } else {
          currentReply.thinkingContent = newVal.replace(/<think>/g, '')
        }
      } else {
        currentReply.thinkingStatus = 'end'
        currentReply.content = newVal.slice(0, newVal.indexOf('echartsJSONData:'))
        currentReply.loading = false
      }
      if (newVal.includes('echartsJSONData:')) {
        const echartsData = newVal
          .slice(newVal.lastIndexOf('echartsJSONData:') + 16)
          .replace(/```json/g, '')
          .replace(/```/g, '')
        try {
          // 尝试解析JSON，如果成功说明是完整的JSON字符串
          JSON.parse(echartsData)
          // console.log('echartsJSONData-------', JSON.parse(echartsData))
          currentReply.echartsJSONData = JSON.parse(echartsData)
        } catch (error) {
          // JSON解析失败，说明数据不完整，不赋值
          console.log('echartsJSONData 数据不完整，等待更多数据...')
        }
      }
    }
  })

  // 处理引用
  const quoteList = computed(() => {
    if (!data.value.length) return ''
    for (let index = 0; index < data.value.length; index++) {
      const chunk = data.value[index].data
      if (chunk) {
        try {
          const parsedChunk = JSON.parse(chunk)
          if (parsedChunk.event === 'message_end') {
            if (parsedChunk?.metadata?.retriever_resources?.length > 0) {
              return parsedChunk?.metadata?.retriever_resources.map((item) => {
                return {
                  title: item.document_name,
                  docId: item.document_id
                }
              })
            }
          }
        } catch (error) {
          console.error('解析文档列表时出错:', error)
        }
      }
    }
    return []
  })
  watch(quoteList, async (newVal) => {
    if (newVal?.length > 0) {
      console.log('引用列表-------', newVal)
      const currentReply = list.value.find((_) => _.key === key.value)
      const documentIdList = [...new Set(newVal.map((item) => item.docId))]
      const res = await lib.api.knowledgeFileApi.getFromDocumentId({
        documentIdList: documentIdList
      })
      currentReply.quoteList = res?.result || []
    }
  })
  return {
    inputValue,
    recording,
    submitLoading,
    list,
    showSender,
    currentConversationId,
    isCarryHistory,
    handleRecord,
    handleCancel,
    handleSubmit,
    handleClear,
    handleThinkingChange
  }
}
