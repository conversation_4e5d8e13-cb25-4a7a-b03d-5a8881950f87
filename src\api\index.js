import request from '@/utils/request'

export const getDLLData = (url, params) => {
  return request({
    // url: 'rest/ConfigData/direct/DLL1/' + url, // 测服
    url: 'rest/ConfigData/direct/DLL/' + url, // 正服
    method: 'post',
    data: params
  })
}
export const commonalityApi = {
  // 公共函数
  // 获取所有字典map对象
  getDictMap: (params) => {
    return getDLLData('rest/dicDiy/list', params)
  },
  // 根据id获取图片
  getPicture: (params) =>
    request({
      url: 'rest/doc/attachList',
      method: 'post',
      data: params
    })
  // // 获取前缀
  // getServerUrl: (params) =>
  //   request({
  //     url: 'rest/doc/serverUrl',
  //     method: 'post',
  //     data: params
  //   })
}
export const trafficDataApi = {
  getDayData: (params) =>
    request({
      url: 'rest/trafficData/getDayData',
      method: 'post',
      data: params
    }),

  getLeidaData: (params) =>
    request({
      url: 'rest/trafficData/getLeidaData',
      method: 'post',
      data: params
    }),
  getLeidaHourData: (params) =>
    request({
      url: '/rest/trafficData/getLeidaHourData',
      method: 'post',
      data: params
    }),
  getPassRate: (params) =>
    request({
      url: '/rest/trafficData/getPassRate',
      method: 'post',
      data: params
    }),
  getPassRateWithSaturation: (params) =>
    request({
      url: '/rest/trafficData/getPassRateWithSaturation',
      method: 'post',
      data: params
    }),
  getCarTrafficList: (params) =>
    request({
      url: '/rest/trafficData/getCarTrafficList',
      method: 'post',
      data: params
    })
}

export const bigscreenApi = {
  // 机电监测-设备列表
  machineMonitorDeviceList: (params) =>
    request({
      url: 'rest/bigscreen/machineMonitorDeviceList',
      method: 'post',
      data: params
    }),
  // 机电监测-数据
  machineMonitorData: (params) =>
    request({
      url: 'rest/bigscreen/machineMonitorData',
      method: 'post',
      data: params
    }),
  // 养护运营列表(弹窗)
  workOrderList: (params) =>
    request({
      url: 'rest/bigscreen/workOrderList',
      method: 'post',
      data: params
    }),
  // 节点任务接口
  pointTask: (params) =>
    request({
      url: 'rest/bigscreen/pointTask',
      method: 'post',
      data: params
    }),
  // 设备寿命统计
  deviceLifeStatistics: (params) =>
    request({
      url: 'rest/bigscreen/deviceLifeStatistics',
      method: 'post',
      data: params
    }),
  // 设备列表
  deviceSystemNotEmptyList: (params) =>
    request({
      url: 'rest/bigscreen/deviceSystemNotEmptyList',
      method: 'post',
      data: params
    }),
  // 交通监测-车型占比
  vehicleType: (params) =>
    request({
      url: 'rest/bigscreen/vehicleType',
      method: 'post',
      data: params
    }),
  // 交通监测-折线图
  trafficlist: (params) =>
    request({
      url: 'rest/bigscreen/trafficList',
      method: 'post',
      data: params
    }),
  // 作业完成情况
  workOrderStatistics: (params) =>
    request({
      url: 'rest/bigscreen/workOrderStatistics',
      method: 'post',
      data: params
    }),
  // 作业内容列表
  workOrderContentList: (params) =>
    request({
      url: 'rest/bigscreen/workOrderContentList',
      method: 'post',
      data: params
    }),

  getProjectDeviceGroupList: (params) =>
    request({
      url: 'rest/bigscreen/getProjectDeviceGroupList',
      method: 'post',
      data: params
    }),
  deviceGroupData: (params) =>
    request({
      url: 'rest/bigscreen/deviceGroupData',
      method: 'post',
      data: params
    }),
  getIotDataList: (params) =>
    request({
      url: 'rest/bigscreen/getIotDataList',
      method: 'post',
      data: params
    }),
  emergencyFrequencyLevel: (params) =>
    request({
      url: 'rest/bigscreen/emergencyFrequencyLevel',
      method: 'post',
      data: params
    }),
  emergencyAnalysis: (params) =>
    request({
      url: 'rest/bigscreen/emergencyAnalysis',
      method: 'post',
      data: params
    }),
  structutrScienceDecision: (params) =>
    request({
      url: 'rest/bigscreen/structutrScienceDecision',
      method: 'post',
      data: params
    }),
  getModeCodeMileageRelation: (params) =>
    request({
      url: 'rest/bigscreen/getModeCodeMileageRelation ',
      method: 'post',
      data: params
    }),
  getMileageList: (params) =>
    request({
      url: 'rest/bigscreen/getMileageList',
      method: 'post',
      data: params
    }),
  getDeviceTreeType: (params) =>
    request({
      url: 'rest/bigscreen/getDeviceTreeType',
      method: 'post',
      data: params
    }),
  getDeviceModelWithMileage: (params) =>
    request({
      url: 'rest/bigscreen/getDeviceModelWithMileage',
      method: 'post',
      data: params
    }),
  getDeviceLeafMileage: (params) =>
    request({
      url: 'rest/bigscreen/getDeviceLeafMileage',
      method: 'post',
      data: params
    }),
  structureMonitor: (params) =>
    request({
      url: 'rest/bigscreen/structureMonitor',
      method: 'post',
      data: params
    }),
  structureMonitorStatistics: (params) =>
    request({
      url: 'rest/bigscreen/structureMonitorStatistics',
      method: 'post',
      data: params
    }),
  structureMonitorDevice: (params) =>
    request({
      url: 'rest/bigscreen/structureMonitorDevice',
      method: 'post',
      data: params
    }),
  emergencyStatistics: (params) =>
    request({
      url: 'rest/bigscreen/emergencyStatistics',
      method: 'post',
      data: params
    }),
  emergencyEvaluation: (params) =>
    request({
      url: 'rest/bigscreen/emergencyEvaluation',
      method: 'post',
      data: params
    }),
  getDeviceEmergencyStatistics: (params) =>
    request({
      url: 'rest/bigscreen/getDeviceEmergencyStatistics',
      method: 'post',
      data: params
    }),
  getEvaluatePythonPushType: (params) =>
    request({
      url: 'rest/bigscreen/getEvaluatePythonPushType',
      method: 'post',
      data: params
    }),
  smartMaintenance: (params) =>
    request({
      url: 'rest/bigscreen/smartMaintenance',
      method: 'post',
      data: params
    }),
  deviceMonitor: (params) =>
    request({
      url: 'rest/bigscreen/deviceMonitor',
      method: 'post',
      data: params
    }),
  jobControl: (params) =>
    request({
      url: 'rest/bigscreen/jobControl',
      method: 'post',
      data: params
    }),
  accurateEvaluation: (params) =>
    request({
      url: 'rest/bigscreen/accurateEvaluation',
      method: 'post',
      data: params
    }),
  getDefectOrders: (params) =>
    request({
      url: 'rest/bigscreen/getDefectOrders',
      method: 'post',
      data: params
    }),
  getWeather: (params) =>
    request({
      url: 'rest/bigscreen/getWeather',
      method: 'post',
      data: params
    }),
  congestion: (params) =>
    request({
      url: 'rest/bigscreen/congestion',
      method: 'post',
      data: params
    }),
  channelAlert: (params) =>
    request({
      url: 'rest/bigscreen/channelAlert',
      method: 'post',
      data: params
    }),
  getDutyInfo: (params) =>
    request({
      url: 'rest/bigscreen/getDutyInfo',
      method: 'post',
      data: params
    }),
  saveDutyInfo: (params) =>
    request({
      url: 'rest/bigscreen/saveDutyInfo',
      method: 'post',
      data: params
    }),
  presetStructureGet: (params) =>
    request({
      url: 'rest/bigscreen/presetStructureGet',
      method: 'post',
      data: params
    })
}
export const configviewpointApi = {
  getViewPoint: (params) =>
    request({
      url: 'rest/configViewpoint/getViewPoint',
      method: 'post',
      data: params
    })
}
export const evaluatePythonPushApi = {
  list: (params) => {
    return getDLLData('rest/evaluatePythonPush/list', params)
  }
}

export const roadRatingApi = {
  getRating: (params) =>
    request({
      url: 'rest/roadRating/getRating',
      method: 'post',
      data: params
    }),
  roadQuality: (params) =>
    request({
      url: 'rest/roadRating/roadQuality',
      method: 'post',
      data: params
    })
}

export const bigScreePdRoadControllerApi = {
  emergencyStatistics: (params) => {
    return getDLLData('rest/bigScreePdRoadController/emergencyStatistics', params)
  },
  smartMaintenance: (params) => {
    return getDLLData('rest/bigScreePdRoadController/smartMaintenance', params)
  },
  lifeEvaluateCycle: (params) => {
    return getDLLData('rest/bigScreePdRoadController/lifeEvaluateCycle', params)
  },
  deviceResumeCard: (params) => {
    return getDLLData('rest/bigScreePdRoadController/deviceResumeCard', params)
  },
  emergencyAssess: (params) => {
    return getDLLData('rest/bigScreePdRoadController/emergencyAssess', params)
  },
  getEmergency: (params) => {
    return getDLLData('rest/bigScreePdRoadController/getEmergency', params)
  }
}

export const pdInspectionMaintenanceApi = {
  getMaintenanceOrders: (params) => {
    return getDLLData('rest/pdInspectionMaintenance/getMaintenanceOrders', params)
  },
  getMaintenanceOrderDetails: (params) => {
    return getDLLData('rest/pdInspectionMaintenance/getMaintenanceOrderDetails', params)
  },
  jobControl: (params) => {
    return getDLLData('rest/pdInspectionMaintenance/jobControl', params)
  },
  getDefectOrders: (params) => {
    return getDLLData('rest/pdInspectionMaintenance/getDefectOrders', params)
  },
  geDefectOrderDetails: (params) => {
    return getDLLData('rest/pdInspectionMaintenance/geDefectOrderDetails', params)
  }
}

export const pdRoadDeviceAlertApi = {
  deviceAlertStatistics: (params) => {
    return getDLLData('rest/pdRoadDeviceAlert/deviceAlertStatistics', params)
  },
  getDeviceModel: (params) => {
    return getDLLData('rest/pdRoadDeviceAlert/getDeviceModel', params)
  },
  getDeviceList: (params) =>
    request({
      url: 'rest/bigscreen/getDeviceList',
      method: 'post',
      data: params
    })
}

export const pdRoadEmergencyManageApi = {
  tractionEvaluation: (params) => {
    return getDLLData('rest/pdRoadEmergencyManage/tractionEvaluation', params)
  }
}

export const deviceManageApi = {
  monitorList: (params) => {
    return getDLLData('rest/deviceManage/monitorList', params)
  }
}

export const carStatisticsApi = {
  dailYaverage: (params) => {
    return getDLLData('rest/carStatistics/dailYaverage', params)
  }
}
// 交通运行
export const trafficOperationApi = {
  // 同比率 环比率
  dailYaverage: (params) =>
    request({
      url: 'rest/trafficData/dailYaverage',
      method: 'post',
      data: params
    })
}
// 动态养护
export const advqjInspectionMaintenanceApi = {
  // 作业管控
  jobControl: (params) => {
    return getDLLData('rest/advqjInspectionMaintenance/jobControl', params)
  },
  // 维养作业/巡检巡视
  getMaintenanceOrders: (params) => {
    return getDLLData('rest/advqjInspectionMaintenance/getMaintenanceOrders', params)
  },
  // 养护运营
  maintenanceDataDetail: (params) =>
    request({
      url: 'rest/bigscreen/maintenanceDataDetail',
      method: 'post',
      data: params
    }),
  // 节点任务
  maintenanceNodeDetail: (params) =>
    request({
      url: 'rest/bigscreen/maintenanceNodeDetail',
      method: 'post',
      data: params
    })
}

// 作业
export const workOrderApi = {
  // 作业列表
  maintenanceList: (params) => {
    return getDLLData('rest/workOrder/maintenanceList', params)
  }
}

export const dicDiyApi = {
  list: (params) => {
    return getDLLData('rest/dicDiy/list', params)
  }
}
// 作业详情
export const workOrderDetailApi = {
  getMaintenanceOrderDetails: (params) => {
    return getDLLData('rest/pdInspectionMaintenance/getMaintenanceOrderDetails', params)
  },
  processData: (params) => {
    return getDLLData('rest/workOrder/processData', params)
  }
}
// 缺陷详情
export const defectOrderDetailApi = {
  geDefectOrderDetails: (params) => {
    return getDLLData('rest/pdInspectionMaintenance/geDefectOrderDetails', params)
  }
}
// 事件详情
export const eventOrderDetailApi = {
  getEmergency: (params) => {
    return getDLLData('rest/bigScreePdRoadController/getEmergency', params)
  }
}

export const presetStructureApi = {
  get: (params) => {
    return getDLLData('rest/presetStructure/get', params)
  }
}
// 监测分析接口
export const monitorAnalysisApi = {
  deviceGroupData: (params) =>
    request({
      url: 'rest/bigscreen/deviceGroupData',
      method: 'post',
      data: params
    }),
  deviceGroupDataDetail: (params) =>
    request({
      url: 'rest/bigscreen/deviceGroupDataDetail',
      method: 'post',
      data: params
    })
}
// 撒点相关
export const getPointCode = {
  getModeCodeFromMileage: (params) =>
    request({
      url: 'rest/bigscreen/getModeCodeFromMileage',
      method: 'post',
      data: params
    })
}
// 设备档案
export const getPopupDetail = {
  deviceResumeCard: (params) =>
    request({
      url: 'rest/bigscreen/deviceResumeCard',
      method: 'post',
      data: params
    }),
  // 不含养护计划和维养计划
  deviceResumeCardDevice: (params) =>
    request({
      url: 'rest/bigscreen/deviceResumeCardDevice',
      method: 'post',
      data: params
    }),
  // 只含养护计划
  deviceResumeCardMaintenanceWorkPlan: (params) =>
    request({
      url: 'rest/bigscreen/deviceResumeCardMaintenanceWorkPlan',
      method: 'post',
      data: params
    }),
  // 只含维养作业
  deviceResumeCardMainTenanceOrder: (params) =>
    request({
      url: 'rest/bigscreen/deviceResumeCardMainTenanceOrder',
      method: 'post',
      data: params
    }),
  structureResumeCard: (params) =>
    request({
      url: 'rest/bigscreen/structureResumeCard',
      method: 'post',
      data: params
    }),
  // 只含基本信息
  structureResumeCardStructure: (params) =>
    request({
      url: 'rest/bigscreen/structureResumeCardStructure',
      method: 'post',
      data: params
    }),
  // 只含养护计划
  structureResumeCardMaintenanceWorkPlan: (params) =>
    request({
      url: 'rest/bigscreen/structureResumeCardMaintenanceWorkPlan',
      method: 'post',
      data: params
    }),
  // 只含维养作业
  structureResumeCardMainTenanceOrder: (params) =>
    request({
      url: 'rest/bigscreen/structureResumeCardMainTenanceOrder',
      method: 'post',
      data: params
    }),
  // 只含缺陷记录
  structureResumeCardDefect: (params) =>
    request({
      url: 'rest/bigscreen/structureResumeCardDefect',
      method: 'post',
      data: params
    }),
  // 只含结构报警
  structureResumeCardStructureAlert: (params) =>
    request({
      url: 'rest/bigscreen/structureResumeCardStructureAlert',
      method: 'post',
      data: params
    })
}

export const docDiyDiyApi = {
  getDocsFromUrl: (params) =>
    request({
      url: 'rest/docDiyDiy/getDocsFromUrl',
      method: 'post',
      data: params
    }),
  getDocs: (params) =>
    request({
      url: 'rest/docDiyDiy/getDocs',
      method: 'post',
      data: params
    })
}
export const getWorkControlApi = {
  workOrderPicList: (params) =>
    request({
      url: 'rest/bigscreen/workOrderPicList',
      method: 'post',
      data: params
    }),
  taskPicList: (params) =>
    request({
      url: 'rest/bigscreen/taskPicList',
      method: 'post',
      data: params
    })
}

export const questionApi = {
  // 分页查询问题表列表
  list: (params) =>
    request({
      url: 'rest/question/list',
      method: 'post',
      data: params
    })
}

export const knowledgeFileApi = {
  getFromDocumentId: (params) =>
    request({
      url: 'rest/knowledgeFile/getFromDocumentId',
      method: 'post',
      data: params
    })
}
