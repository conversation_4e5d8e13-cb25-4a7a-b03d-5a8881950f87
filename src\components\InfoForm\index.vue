<template>
  <div class="info-from-container" :style="{ width }">
    <div class="info-row" v-for="(item, i) in Math.ceil(data.length / 2)" :key="item">
      <div class="info-col" v-for="k in [0, 1]" :key="k">
        <template v-if="data[i * 2 + k]">
          <span class="title">{{ data[i * 2 + k].name + '：' }}</span>
          <span class="value" v-tooltip>{{ data[i * 2 + k].value }}</span>
        </template>
      </div>
    </div>
    <div class="line"></div>
  </div>
</template>
<script setup>
  defineProps({
    data: {
      type: Array,
      default: () => []
    },
    width: {
      type: String,
      default: '100%'
    }
  })
</script>

<style lang="scss" scoped>
  .info-from-container {
    position: relative;
    .info-row {
      display: flex;
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      .info-col {
        display: flex;

        // flex: 1;
        width: 201px;
        font-family: Alibaba-PuHuiTi;
        font-size: 14px;
        font-weight: normal;
        line-height: 16px;
        &:nth-child(odd) {
          padding-right: 24px;
        }
        &:nth-child(even) {
          padding-left: 24px;
        }
        .title {
          color: #ffffff;
        }
        .value {
          flex: 1;
          color: #00c3ff;
        }
      }
    }
    .line {
      position: absolute;
      top: 0;
      left: calc(50% - 0.5px);
      width: 1px;
      height: 100%;
      background: linear-gradient(180deg, rgb(61 109 133 / 0%) 0%, #3c87ac 50%, rgb(61 109 133 / 0%) 100%);
      border-radius: 1px;
    }
  }
</style>
