import { createRouter, createWebHashHistory } from 'vue-router'

// import Screen from '@/views/Screen/index.vue'
// import BottomNavigation from '@/views/Screen/ScreenMiddlePage/Components/BottomNavigation/index.vue'
// import TractionEvaluation from '@/views/Screen/ScreenMiddlePage/Components/Emergency/Components/TractionEvaluation.vue'
// import InformationBoard from '@/views/Screen/ScreenMiddlePage/Components/Traffic/Components/Info.vue'
// import InformationBoardLane from '@/views/Screen/ScreenMiddlePage/Components/Traffic/Components/InfoLane.vue'
const routes = [
  {
    path: '/',
    name: 'AiChatPage',
    component: () => import('@/views/AIChatPage/index.vue')
  },
  {
    path: '/Screen',
    name: 'Screen',
    component: () => import('@/views/Screen/index.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
