// FileList 组件类型定义

export interface FileItem {
  uid: string
  name: string
  size: number
  type: 'document' | 'image' | 'video' | 'audio' | 'unknown'
  raw?: File
  url?: string
  isRemote?: boolean
  status: 'ready' | 'uploading' | 'success' | 'error'
  progress: number
  uploadId?: string
}

export interface FileListProps {
  modelValue?: FileItem[]
  height?: string
  disabled?: boolean
}

export interface FileListEmits {
  (e: 'update:modelValue', value: FileItem[]): void
  (e: 'file-remove', file: FileItem, index: number): void
  (e: 'change', fileList: FileItem[]): void
}

export interface DifyUploadResponse {
  id: string
  name: string
  size: number
  extension: string
  mime_type: string
  created_by: string
  created_at: number
}

export interface FileUploadConfig {
  image_file_size_limit?: number
  video_file_size_limit?: number
  audio_file_size_limit?: number
  file_size_limit?: number
  document_file_size_limit?: number
}

export interface FileUploadSettings {
  allowed_file_types?: string[]
  allowed_file_upload_methods?: string[]
  number_limits?: number
  enabled?: boolean
  fileUploadConfig?: FileUploadConfig
}
