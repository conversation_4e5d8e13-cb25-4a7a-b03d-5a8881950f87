/**
 * Demo client for integrating with Dify Chatbox iframe
 * This file demonstrates how a parent application can communicate with an embedded Dify Chatbot
 */
import type { MessageTypeEnum } from './DifyChatbot';
/**
 * Example implementation of parent-side communication with Dify Chatbox
 */
export declare class DifyChatboxComunication {
    private iframe;
    private iframeReady;
    private sendToIframe;
    private currentConversationId;
    private messageHandlers;
    /**
     * Initialize the Dify Chatbox client
     * @param iframeElement - The iframe element containing Dify Chatbox
     */
    constructor(iframeElement: HTMLIFrameElement);
    /**
     * Set up event listener for messages from the iframe
     */
    private setupMessageListener;
    /**
     * Handle chatbox ready message
     */
    private handleChatboxReady;
    /**
     * Handle conversation completed message
     */
    private handleConversationCompleted;
    /**
     * Handle streaming response chunks
     */
    private handleResponseStream;
    /**
     * Handle user message sent notification
     */
    private handleUserMessageSent;
    /**
     * Handle conversation history
     */
    private handleConversationHistory;
    /**
     * Handle error message
     */
    private handleError;
    /**
     * Handle feedback update
     */
    private handleFeedbackUpdated;
    /**
     * Send a message to the chatbox
     * @param query - The message text
     * @param files - Optional files to attach
     * @param inputs - Optional input variables
     */
    sendMessage({ query, files, inputs, }: {
        query: string;
        files?: any[];
        inputs?: Record<string, any>;
    }): void;
    /**
     * Clear the conversation
     */
    clearConversation(): void;
    /**
     * Set input variables
     * @param inputs - Key-value pairs of input variables
     */
    setInputs(inputs: Record<string, any>): void;
    /**
     * Request conversation history
     */
    getConversationHistory(): void;
    /**
     * Get current status
     */
    getStatus(): void;
    /**
     * Set interface language
     * @param language - Language code
     */
    setLanguage(language: string): void;
    /**
     * Register a custom handler for a message type
     * @param type - Message type to handle
     * @param handler - Callback function
     */
    on(type: MessageTypeEnum, handler: (data: any) => void): void;
}
/**
 * Usage example
 */
export declare function createDifyChatboxDemo(): void;
