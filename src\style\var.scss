$screen-width: 5120px; //屏幕宽度
$screen-height: 1440px; //屏幕高度

$screen-top-height: 98px; //顶部高度

$screen-left-width: 1395px; //左侧宽度
$screen-left-height: 1347px; //左侧高度

$screen-right-width: 1395px; //右侧宽度
$screen-right-height: 1347px; //右侧高度

.scroll-bar-style {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: #021348;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 3px rgba(0.4, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #021348;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    background-color: #07699a;
  }
}

body{
  font-family: Alibaba-PuHuiTi;
  // background-color: #021348;
}


.tooltip-style{
  max-width: 200px;
}

.cursor-help{
  cursor: help !important;
}

.anchorBL {
  display: none;
}