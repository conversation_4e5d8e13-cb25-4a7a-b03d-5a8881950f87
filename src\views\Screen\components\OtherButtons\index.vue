<template>
  <div class="ai-buttons-container" style="height:80px;">
    <div v-for="item in list" :key="item.id" class="img-wrapper">
      <img
        class="w91 h77 cursor-pointer"
        :src="item.selected ? item.iconSelected : item.icon"
        @mouseenter="showTooltip(item)"
        @mouseleave="hideTooltip(item, $event)"
        @click="throttledClick(item)" />
      <div class="tooltip-container">
        <transition name="tooltip-slide">
          <div v-if="showTip && currentItem === item && item.name !== '视角'" class="custom-tooltip">
            {{ item.name }}
          </div>
        </transition>
      </div>
    </div>
    <SelectViewPort
      v-show="currentItem && currentItem.id == 9"
      ref="refSelectViewPort"
      @mouseenter="handleViewPortEnter"
      @mouseleave="handleViewPortLeave"
      @clearShow="clearShow"></SelectViewPort>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import lib from '@/utils/lib'
  import AIChat from '@/components/AIChat/index.vue'
  import SelectViewPort from '../selectViewPort.vue'
  import { useThrottleFn } from '@vueuse/core'
  import { usePopWindow, type PopWindowProps, closePopWindowByTag } from 'znyg-frontend-common'
  import YangHu from '@/components/YangHu/index.vue'
  const { showPopWindow } = usePopWindow()
  const { storeScreenData } = lib.store()
  const refSelectViewPort = ref(null)
  const props = defineProps({
    showModel: {
      type: Boolean,
      default: false
    },
    clearSelected: {
      type: Boolean,
      default: false
    }
  })

  watch(
    () => props.showModel,
    (newVal) => {
      if (newVal) {
        list.value = defaultList.value
      } else {
        list.value = defaultList.value.slice(0, 3)
      }
    }
  )
  watch(
    () => props.clearSelected,
    (newVal) => {
      if (newVal) {
        list.value[2].selected = false
      }
    }
  )
  const list = ref([
    {
      id: 1,
      name: '智慧AI',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn1.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn1Selected.png'),
      selected: false
    },
    // {
    //   id: 2,
    //   name: '养护专屏',
    //   icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2.png'),
    //   iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2Selected.png'),
    //   selected: false
    // },
    {
      id: 2,
      name: '开始测试',
      icon: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2.png'),
      iconSelected: lib.utils.getAssetsFile('ScreenMiddle/AiButtons/btn2Selected.png'),
      selected: false
    }
  ])
  const defaultList = ref([...list.value])
  const isQuestionType = computed(() => {
    return lib.store().storeScreenData.isQuestionType
  })
  watch(isQuestionType, (newVal) => {
    list.value[1].selected = newVal
  })
  const emits = defineEmits(['btnClick'])
  const handleClick = async (item) => {
    console.log(item)
    item.selected = !item.selected
    switch (item.id) {
      case 1:
        if (item.selected) {
          openAiChat('')
        } else {
          closePopWindowByTag(lib.enumsList.widows.Ai对话框)
        }
        break
      // case 2:
      //   // storeScreenData.showAIQuestion = item.selected
      //   // lib.store().storeScreenData.isQuestionType = item.selected
      //   if (item.selected) {
      //     openYangHu()
      //   } else {
      //     closePopWindowByTag(lib.enumsList.widows.养护对话框)
      //   }
      //   break
      case 2:
        lib.bus.busAiRobot.emit('首次加载')
        break
    }
  }

  const throttledClick = useThrottleFn(handleClick, 500)
  const openAiChat = (payload: any) => {
    closePopWindowByTag(lib.enumsList.widows.养护对话框)
    const op: PopWindowProps = {
      top: 150,
      left: 350,
      width: 1600,
      height: 1043,
      tag: lib.enumsList.widows.Ai对话框,
      id: 'ai-chat-container',
      appendParent: 'popUpRoot',
      style: {
        backdropFilter: 'blur(18px)',
        zIndex: 99999,
        overflow: 'hidden',
        borderRadius: '26px'
      },
      draggable: true,
      draggableClass: 'drag-container',
      animate: true,
      animateInClass: 'animate__animated animate__faster animate__zoomIn',
      animateOutClass: 'animate__animated animate__faster animate__zoomOut',
      onClose: () => {
        list.value[0].selected = false
      }
    }
    showPopWindow(op, AIChat, { question: payload })
  }

  const openYangHu = (type: '养护' | '浙江区域') => {
    const op: PopWindowProps = {
      top: 200,
      left: -1200,
      width: 3500,
      height: 1000,
      tag: lib.enumsList.widows.养护对话框,
      id: 'ai-robot-container',
      appendParent: 'popUpRoot',
      style: {
        backdropFilter: 'blur(18px)',
        zIndex: 333,
        overflow: 'hidden',
        borderRadius: '26px'
      },
      draggable: true,
      draggableClass: 'drag-container',
      animate: true,
      animateInClass: 'animate__animated animate__faster animate__zoomIn',
      animateOutClass: 'animate__animated animate__faster animate__zoomOut',
      onClose: () => {
        list.value[1].selected = false
      }
    }
    showPopWindow(op, YangHu, { type })
  }

  lib.bus.busAIButtons.on((isShow: boolean, payload: any) => {
    if (isShow) {
      openAiChat(payload)
    } else {
      closePopWindowByTag(lib.enumsList.widows.Ai对话框)
    }
    list.value[0].selected = isShow
  })

  lib.bus.busYangHu.on((isShow: boolean, type: '养护' | '浙江区域') => {
    if (isShow) {
      openYangHu(type)
    } else {
      closePopWindowByTag(lib.enumsList.widows.养护对话框)
    }
    // list.value[1].selected = isShow
  })

  onUnmounted(() => {
    lib.bus.busAIButtons.reset()
    lib.bus.busYangHu.reset()
  })
  // 控制提示框显示状态
  const showTip = ref(false)
  // 当前显示提示框对应的列表项
  const currentItem = ref(null)
  // 显示提示框
  const showTooltip = (item) => {
    // 清除之前的延迟隐藏定时器
    showTip.value = true
    currentItem.value = item
  }
  // 增加关闭定时器
  let closeTimer = null
  // 隐藏提示框
  const hideTooltip = (item, event) => {
    // 如果鼠标移入到Select组件区域，则不关闭
    const selectViewPortEl = refSelectViewPort.value?.$el
    if (selectViewPortEl?.contains(event.relatedTarget)) {
      return
    }
    // 延迟关闭以便给鼠标进入Select组件留出时间
    closeTimer = setTimeout(() => {
      if (currentItem.value === item) {
        showTip.value = false
        currentItem.value = null
      }
    }, 150) // 150ms延迟足够完成鼠标移动
  }
  // 处理进入Select组件
  const handleViewPortEnter = () => {
    // 清除关闭定时器
    clearTimeout(closeTimer)
    closeTimer = null
  }
  let isShow = ref(true) // 新增一个变量来跟踪是否显示Select组件
  // 处理离开Select组件
  const handleViewPortLeave = () => {
    // 重新设置关闭定时器
    closeTimer = setTimeout(() => {
      if (isShow.value) {
        currentItem.value = null
      }
    }, 150)
  }
  const clearShow = (bool = true) => {
    isShow.value = bool
    handleViewPortLeave()
  }
</script>

<style lang="scss" scoped>
  .ai-buttons-container {
    position: absolute;
    top: 20px;
    left: 70px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 91px;
    height: 826px;
  }
  .img-wrapper {
    position: relative;
  }
  .tooltip-container {
    position: absolute;
    top: 0;
    left: 100%;
    display: flex;
    align-items: center;
    height: 100%;
  }
  .custom-tooltip {
    z-index: 1000;
    width: 120px;
    height: 40px;
    font-family: 'Source Han Sans CN';
    font-size: 20px;
    font-weight: 400;
    line-height: 40px;
    color: #ffffff;
    text-align: center;
    background: rgb(81 120 212 / 45%);
    border-radius: 6px;
    box-shadow: 0 3px 10px 1px rgb(0 0 0 / 16%);
  }

  /* 定义 tooltip-slide 动画样式 */
  .tooltip-slide-enter-active,
  .tooltip-slide-leave-active {
    transition: all 1s ease;
  }
  .tooltip-slide-enter-from,
  .tooltip-slide-leave-to {
    opacity: 0;
    transform: translateX(-10px);
  }
  .tooltip-slide-enter-to,
  .tooltip-slide-leave-from {
    opacity: 1;
    transform: translateX(0);
  }
</style>
