<template>
	<div class="ai-message">
		<div class="message-content">
			<!-- 思考过程显示 -->
			<div v-if="thinking.content" class="thinking-section">
				<details :open="thinking.showOpen">
					<summary class="thinking-summary">
						<!-- <span class="thinking-icon">🤔</span> -->
						<span>思考过程</span>
					</summary>
					<div class="thinking-content" v-html="thinking.renderedContent"></div>
					<!-- <XMarkdown :markdown="thinking.renderedContent" v-if="thinking.renderedContent" class="markdown-block" /> -->
				</details>
			</div>

			<!-- 主要回复内容 -->
			<div v-if="markdownContent" class="main-content">
				<!-- 非 markdown 部分 -->
				<!-- <div v-html="parsedContent.text" class="rendered-content"></div> -->
				<!-- markdown 渲染 -->
				<XMarkdown :markdown="markdownContent" v-if="markdownContent" class="markdown-block" />
			</div>
			<!-- 无 markdown -->
			<div v-else="parsedContent.text" class="markdown-block">
				<!-- <XMarkdown :markdown="parsedContent.renderedText" /> -->
				<div v-html="parsedContent.text"></div>
			</div>

			<!-- ECharts 图表 -->
			<div v-if="echartsData" class="echarts-container">
				<VChart :key="chartKey" :option="echartsData" style="width: 100%; height: 600px;" />
			</div>

			<!-- 时间戳 -->
			<div v-if="timestamp" class="message-time">
				{{ new Date(timestamp).toLocaleTimeString() }}
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import VChart from 'vue-echarts'
import { XMarkdown } from 'vue-element-plus-x'


// import 'highlight.js/styles/atom-one-dark.css' // Atom One Dark 风格
// import 'highlight.js/styles/github.css'   // GitHub 风格
// import markdownit from 'markdown-it'
// import hljs from 'highlight.js' // https://highlightjs.org
// Actual default values
// const md = markdownit({
//   highlight: function (str, lang) {
//     if (lang && hljs.getLanguage(lang)) {
//       try {
//         return '<pre><code class="hljs">' +
//                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
//                '</code></pre>';
//       } catch (__) {}
//     }
//     return '<pre><code class="hljs">' + md.utils.escapeHtml(str) + '</code></pre>';
//   }
// });

interface Props {
	content?: string
	timestamp?: string
	loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	content: '',
	timestamp: '',
	loading: false
})

const aa = {}

// 响应式数据
const thinking = ref({
	content: '',
	renderedContent: '',
	showOpen: false
})

const parsedContent = ref({
	text: '',
	renderedText: ''
})
const markdownContent = ref('')

const echartsData = ref<any>(null)
const chartKey = ref(0)

// 解析内容的函数
const parseContent = (content: string) => {
	if (!content) {
		// 清空所有状态
		thinking.value.content = ''
		thinking.value.renderedContent = ''
		parsedContent.value.text = ''
		markdownContent.value = ''
		echartsData.value = null
		return
	}

	let workingContent = content

	// 重置所有状态
	thinking.value.content = ''
	thinking.value.renderedContent = ''
	parsedContent.value.text = ''
	markdownContent.value = ''
	echartsData.value = null

	// 1. 提取思考过程 <think>...</think>
	const thinkRegex = /<think>([\s\S]*?)<\/think>/g
	let thinkMatch = thinkRegex.exec(workingContent)
	if (thinkMatch) {
		thinking.value.content = thinkMatch[1].trim()
		thinking.value.renderedContent = thinking.value.content
		thinking.value.showOpen = false
		// 从内容中移除思考部分
		workingContent = workingContent.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
	}

	// 2. 提取 echarts 代码块
	const echartsRegex = /```echarts\s*([\s\S]*?)```/g
	let echartsMatch = echartsRegex.exec(workingContent)
	if (echartsMatch) {
		try {
			const echartsJsonStr = echartsMatch[1].trim()
			const parsedEchartsData = JSON.parse(echartsJsonStr)
			echartsData.value = parsedEchartsData
			chartKey.value++ // 强制重新渲染图表

			// 从内容中移除 echarts 代码块
			workingContent = workingContent.replace(/```echarts\s*[\s\S]*?```/g, '').trim()
		} catch (error) {
			console.log('ECharts JSON 解析失败:', error)
		}
	}

	// 3. 检查是否有 echartsJSONData: 格式的数据
	if (workingContent.includes('echartsJSONData:')) {
		const echartsDataStart = workingContent.indexOf('echartsJSONData:')
		const beforeEchartsData = workingContent.slice(0, echartsDataStart).trim()
		const echartsDataStr = workingContent.slice(echartsDataStart + 16).replace(/```json/g, '').replace(/```/g, '').trim()

		try {
			const parsedEchartsData = JSON.parse(echartsDataStr)
			echartsData.value = parsedEchartsData
			chartKey.value++
			workingContent = beforeEchartsData
		} catch (error) {
			console.log('echartsJSONData 解析失败:', error)
		}
	}

	// 4. 所有剩余内容（包括普通代码块）作为主要 markdown 内容
	parsedContent.value.text = workingContent
	markdownContent.value = workingContent

}

// 监听 content 变化
watch(() => props.content, (newContent) => {
	parseContent(newContent)
}, { immediate: true })

// 监听 props 变化，确保在切换对话时重置状态
watch(() => [props.content, props.timestamp], () => {
	// 强制重新渲染图表
	chartKey.value++
}, { deep: true })

// 初始化时解析内容
onMounted(() => {
	if (props.content) {
		console.log('Initializing content parsing:', props.content);
		parseContent(props.content)
	}
})
</script>

<style scoped>
.ai-message {
	/* padding: 12px 16px;
  background: #f5f5f5; */
	border-radius: 18px;
	color: #333;
	font-size: 16px;
	line-height: 1.5;
	max-width: 100%;
	word-wrap: break-word;
}

.message-content {
	display: flex;
	flex-direction: column;
	/* gap: 12px; */
}

.thinking-section {
	margin-bottom: 8px;
}

.thinking-summary {
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 6px;
	font-weight: 500;
	color: #666;
	user-select: none;
	padding: 8px 12px;
	background: #e8f4f8;
	border-radius: 8px;
	border-left: 4px solid #2196f3;
}

.thinking-summary:hover {
	background: #d1e9f3;
}

.thinking-icon {
	font-size: 16px;
}

.thinking-content {
	margin-top: 8px;
	padding: 12px;
	background: #f9f9f9;
	border-radius: 6px;
	border-left: 3px solid #2196f3;
}

.main-content {
	/* 主要内容样式 */
	line-height: 1.6;
}

.markdown-block {
	/* margin: 8px 0; */
	/* background: #f8f8f8; */
	border-radius: 8px;
	overflow: hidden;
}

.elx-xmarkdown-container p {
	margin: 0;
}

.echarts-container {
	width: 100%;
	display: flex;
	justify-content: center;
	/* background: #ffffff; */
	border-radius: 8px;
	padding: 16px;
	margin: 12px 0;
}

.message-time {
	font-size: 12px;
	color: #90a4ae;
	opacity: 0.8;
	text-align: right;
	/* margin-top: 8px; */
}

/* XMarkdown 组件的样式会由组件本身处理 */
:deep(.elx-xmarkdown-provider) {
	/* 这里可以添加全局的 XMarkdown 样式 */
	p {
		margin: 0;
	}
}
</style>
