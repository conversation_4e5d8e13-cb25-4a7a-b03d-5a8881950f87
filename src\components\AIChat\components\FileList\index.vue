<template>
  <div class="file-list-container" :style="{ height: height }">
    <!-- 使用 FilesCard 组件渲染每个文件 -->
    <div v-if="fileList.length > 0" class="files-card-list">
      <FilesCard
        v-for="(file, index) in fileList"
        :key="file.uid"
        :uid="file.uid"
        :name="file.name"
        :file-size="file.size"
        :file-type="getFilesCardType(file.type)"
        :url="file.url"
        :thumb-url="file.url"
        :img-file="file.raw"
        :show-del-icon="true"
        :status="getFilesCardStatus(file.status)"
        :percent="file.progress"
        :error-tip="file.status === 'error' ? '上传失败' : undefined"
        :style="{ marginBottom: '8px' }"
        @delete="handleRemove(file, index)"
        class="file-card-item"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, readonly } from 'vue'
import { FilesCard } from 'vue-element-plus-x'
import { useAppState } from '@/composables/useAppState'
import { getSupportedExtensions, getFileType, getFileSizeLimit, useAiChat } from '../../helper'
import type { FileItem, FileListProps, FileListEmits, DifyUploadResponse } from './types'

// 组件属性定义
interface Props {
  height?: string
  disabled?: boolean
}

// 组件事件定义
interface Emits {
  (e: 'file-remove', file: FileItem, index: number): void
  (e: 'change', fileList: FileItem[]): void
}

const props = withDefaults(defineProps<Props>(), {
  height: '200px',
  disabled: false
})

const emit = defineEmits<Emits>()

// 使用 helper 中的文件状态和方法
const { uploadedFiles, removeUploadFile } = useAiChat()

// 响应式数据 - 直接使用 helper 中的文件列表
const fileList = uploadedFiles

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFilesCardType = (type: string) => {
  // 将我们的文件类型映射到 FilesCard 支持的类型
  const typeMap = {
    'document': 'file',
    'image': 'image',
    'video': 'video',
    'audio': 'audio',
    'unknown': 'unknown'
  }
  return typeMap[type] || 'file'
}

const getFilesCardStatus = (status: string) => {
  // 将我们的状态映射到 FilesCard 支持的状态
  const statusMap = {
    'uploading': 'uploading',
    'success': 'done',
    'error': 'error',
    'ready': undefined
  }
  return statusMap[status]
}

const getStatusText = (file: FileItem) => {
  switch (file.status) {
    case 'uploading':
      return `上传中... ${file.progress}%`
    case 'success':
      return '上传成功'
    case 'error':
      return '上传失败'
    default:
      return ''
  }
}

// 事件处理
const handleRemove = (file: FileItem, index: number) => {
  removeUploadFile(file.uid)
  emit('file-remove', file, index)
  emitChange()
}

const removeFile = (index: number) => {
  const file = fileList.value[index]
  removeUploadFile(file.uid)
  emit('file-remove', file, index)
  emitChange()
}

const clearAll = () => {
  fileList.value.forEach(file => {
    removeUploadFile(file.uid)
  })
  emitChange()
}

const emitChange = () => {
  emit('change', fileList.value)
}

// 暴露给父组件的方法
defineExpose({
  clearAll,
  fileList: readonly(fileList)
})
</script>

<style scoped lang="scss">
.file-list-container {
  // width: 100%;
	padding: 10px;
  
  .files-card-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
    
    .file-card-item {
      // 自定义 FilesCard 样式
      :deep(.elx-files-card) {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        padding: 12px;
        background-color: #fff;
        transition: all 0.3s;
        
        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        &.elx-files-card-uploading {
          border-color: #409eff;
          background-color: #f0f9ff;
        }
        
        &.elx-files-card-error {
          border-color: #f56c6c;
          background-color: #fef0f0;
        }
        
        &.elx-files-card-done {
          border-color: #67c23a;
          background-color: #f0f9ff;
        }
        
        .elx-files-card-delete-icon {
          color: #f56c6c;
          
          &:hover {
            background-color: rgba(245, 108, 108, 0.1);
          }
        }
      }
    }
  }
}
:deep(.elx-files-card ){
	.elx-files-card-delete-icon {
		top: 0;
		right: 0;
	}
}
</style>
