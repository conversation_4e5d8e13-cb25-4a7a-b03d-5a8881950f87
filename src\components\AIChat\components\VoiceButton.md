# VoiceButton 语音按钮组件

## 功能特性

1. **一键语音输入** - 点击按钮自动连接WebSocket服务并开始录音
2. **集成音量波动** - 按钮内部显示音量波动效果
3. **智能状态管理** - 自动处理连接、录音、断开状态
4. **语音唤醒功能** - 支持唤醒词检测和状态显示

## 使用方法

```vue
<template>
  <VoiceButton
    ref="voiceButtonRef"
    :wakeup-word="'小建同学'"
    server-url="ws://localhost:8865"
    :show-wakeup-word="true"
    @transcription="handleVoiceTranscription"
    @wakeup-changed="handleWakeupChanged"
    @connection-changed="handleConnectionChanged"
  />
</template>

<script setup>
import VoiceButton from './components/VoiceButton.vue'

const handleVoiceTranscription = (text, timestamp) => {
  console.log('语音识别结果:', text)
  // 处理语音识别结果
}

const handleWakeupChanged = (isWakeup) => {
  console.log('唤醒状态变化:', isWakeup)
  // 处理唤醒状态变化
}

const handleConnectionChanged = (isConnected) => {
  console.log('连接状态变化:', isConnected)
  // 处理连接状态变化
}
</script>
```

## 交互逻辑

1. **首次点击**: 连接WebSocket服务 → 开始录音
2. **录音中点击**: 停止录音 → 断开服务
3. **连接但未录音时点击**: 开始录音

## 视觉效果

- **未连接**: 默认透明按钮
- **已连接**: 绿色背景
- **录音中**: 红色背景 + 音量波动效果
- **音量波动**: 三层同心圆波纹，根据音量强度调整透明度

## Props

- `wakeupWord` (string): 唤醒词，默认为 "小建同学"
- `serverUrl` (string): WebSocket服务器地址，默认为 "ws://localhost:8865"
- `showWakeupWord` (boolean): 是否显示唤醒词提示，默认为 true

## Events

- `transcription`: 语音识别结果事件 `(text: string, timestamp: number)`
- `wakeup-changed`: 唤醒状态变化事件 `(isWakeup: boolean)`
- `connection-changed`: 连接状态变化事件 `(isConnected: boolean)`

## 暴露的方法

- `toggleVoiceInput()`: 切换语音输入状态

## 状态

- `isRecording`: 是否正在录音
- `isConnected`: 是否已连接到语音服务
- `isWakeup`: 是否已唤醒

## 语音服务要求

需要运行FunASR语音识别服务，默认端口为8865。
服务应该支持：
- WebSocket连接
- 热词更新
- 实时语音识别
- 唤醒词检测

## 改进内容

1. **简化UI**: 移除了独立的音量条和连接按钮
2. **集成交互**: 一个按钮完成所有操作
3. **视觉反馈**: 按钮内部的波动效果实时反映音量
4. **智能状态**: 自动管理连接和录音状态
