import { ref, computed, watch } from 'vue'

// 应用状态类型定义
export interface AppKeyMap {
  [key: string]: string
}

// 应用参数类型定义
export interface AppParameters {
  opening_statement?: string
  suggested_questions?: string[]
  suggested_questions_after_answer?: {
    enabled: boolean
  }
  speech_to_text?: {
    enabled: boolean
  }
  text_to_speech?: {
    enabled: boolean
    voice?: string
    language?: string
  }
  retriever_resource?: {
    enabled: boolean
  }
  annotation_reply?: {
    enabled: boolean
  }
  more_like_this?: {
    enabled: boolean
  }
  user_input_form?: any[]
  sensitive_word_avoidance?: {
    enabled: boolean
  }
  file_upload?: {
    image?: {
      enabled: boolean
      number_limits?: number
      transfer_methods?: string[]
    }
    enabled: boolean
    allowed_file_types?: string[]
    allowed_file_extensions?: string[]
    allowed_file_upload_methods?: string[]
    number_limits?: number
    fileUploadConfig?: {
      file_size_limit?: number
      batch_count_limit?: number
      image_file_size_limit?: number
      video_file_size_limit?: number
      audio_file_size_limit?: number
      workflow_file_upload_limit?: number
    }
  }
  system_parameters?: {
    image_file_size_limit?: number
    video_file_size_limit?: number
    audio_file_size_limit?: number
    file_size_limit?: number
    workflow_file_upload_limit?: number
  }
  session_id?: string
}

// 默认应用配置
const DEFAULT_APP_KEY_MAP: AppKeyMap = {
  桥梁知识库: 'qiaoliangzhishiku'
  // 智能体1: 'app1',
  // 智能体2: 'app2',
  // 场景操控: 'BimEngineMCPAgent',
  // 知识库: 'qjgfkf',
  // DeepSeek: 'deepseekAndSearch',
  // 控制: 'BimEngineCode2'
}

// 全局状态 - 使用模块级别的状态，保证在整个应用中唯一
let globalAppKeyMap = ref<AppKeyMap>({})
let globalCurrentAppKey = ref<string>('')
let globalAppParameters = ref<AppParameters>({})
let globalAppParametersMap = ref<Record<string, AppParameters>>({})

// 初始化标志
let isInitialized = false

/**
 * 应用状态管理 composable
 * 提供跨组件的共享状态管理
 */
export function useAppState() {
  // 确保只初始化一次
  if (!isInitialized) {
    initializeAppState()
    isInitialized = true
  }

  // 计算属性：当前应用的 appKey 值
  const currentAppKeyValue = computed(() => {
    return globalAppKeyMap.value[globalCurrentAppKey.value] || ''
  })

  // 计算属性：当前应用的参数
  globalAppParameters = computed(() => {
    const currentAppKey = currentAppKeyValue.value
    return globalAppParametersMap.value[currentAppKey] || {}
  })

  // 计算属性：应用名称列表
  const appNames = computed(() => {
    return Object.keys(globalAppKeyMap.value)
  })

  // 获取应用配置映射
  function getAppKeyMap(): AppKeyMap {
    return { ...globalAppKeyMap.value }
  }

  // 设置应用配置映射
  function setAppKeyMap(newAppKeyMap: AppKeyMap) {
    globalAppKeyMap.value = { ...newAppKeyMap }
    localStorage.setItem('appKeyMap', JSON.stringify(newAppKeyMap))

    // 如果当前选中的应用不在新的映射中，切换到第一个
    if (!newAppKeyMap[globalCurrentAppKey.value]) {
      const firstAppName = Object.keys(newAppKeyMap)[0]
      if (firstAppName) {
        setCurrentAppKey(firstAppName)
      }
    }
  }

  // 获取当前选中的应用名称
  function getCurrentAppKey(): string {
    return globalCurrentAppKey.value
  }

  // 设置当前选中的应用
  function setCurrentAppKey(appName: string) {
    if (globalAppKeyMap.value[appName]) {
      globalCurrentAppKey.value = appName
      localStorage.setItem('currentAppKey', appName)
    } else {
      console.warn(`应用 "${appName}" 不存在于 appKeyMap 中`)
    }
  }

  // 获取当前应用的 appKey 值
  function getCurrentAppKeyValue(): string {
    return currentAppKeyValue.value
  }

  // 添加新的应用配置
  function addAppConfig(appName: string, appKey: string) {
    const newAppKeyMap = { ...globalAppKeyMap.value }
    newAppKeyMap[appName] = appKey
    setAppKeyMap(newAppKeyMap)
  }

  // 删除应用配置
  function removeAppConfig(appName: string) {
    const newAppKeyMap = { ...globalAppKeyMap.value }
    delete newAppKeyMap[appName]
    setAppKeyMap(newAppKeyMap)
  }

  // 获取应用参数映射
  function getAppParametersMap(): Record<string, AppParameters> {
    return { ...globalAppParametersMap.value }
  }

  // 设置应用参数映射
  function setAppParametersMap(newAppParametersMap: Record<string, AppParameters>) {
    globalAppParametersMap.value = { ...newAppParametersMap }
    localStorage.setItem('appParametersMap', JSON.stringify(newAppParametersMap))
  }

  // 获取指定应用的参数
  function getAppParameters(appKey: string): AppParameters {
    return globalAppParametersMap.value[appKey] || {}
  }

  // 设置指定应用的参数
  function setAppParameters(appKey: string, parameters: AppParameters) {
    const newAppParametersMap = { ...globalAppParametersMap.value }
    newAppParametersMap[appKey] = parameters
    setAppParametersMap(newAppParametersMap)
  }

  // 获取当前应用的参数
  function getCurrentAppParameters(): AppParameters {
    return globalAppParameters.value
  }

  return {
    // 响应式状态
    appKeyMap: globalAppKeyMap,
    currentAppKey: globalCurrentAppKey,
    currentAppKeyValue,
    globalAppParameters,
    appNames,

    // 方法
    getAppKeyMap,
    setAppKeyMap,
    getCurrentAppKey,
    setCurrentAppKey,
    getCurrentAppKeyValue,
    addAppConfig,
    removeAppConfig,
    getAppParametersMap,
    setAppParametersMap,
    getAppParameters,
    setAppParameters,
    getCurrentAppParameters
  }
}

// 初始化应用状态
function initializeAppState() {
  // 初始化 appKeyMap
  const savedAppKeyMap = localStorage.getItem('appKeyMap')
  if (savedAppKeyMap) {
    try {
      globalAppKeyMap.value = JSON.parse(savedAppKeyMap)
    } catch (error) {
      console.error('解析保存的 appKeyMap 失败:', error)
      globalAppKeyMap.value = { ...DEFAULT_APP_KEY_MAP }
      localStorage.setItem('appKeyMap', JSON.stringify(globalAppKeyMap.value))
    }
  } else {
    globalAppKeyMap.value = { ...DEFAULT_APP_KEY_MAP }
    localStorage.setItem('appKeyMap', JSON.stringify(globalAppKeyMap.value))
  }

  // 初始化 appParametersMap
  const savedAppParametersMap = localStorage.getItem('appParametersMap')
  if (savedAppParametersMap) {
    try {
      globalAppParametersMap.value = JSON.parse(savedAppParametersMap)
    } catch (error) {
      console.error('解析保存的 appParametersMap 失败:', error)
      globalAppParametersMap.value = {}
      localStorage.setItem('appParametersMap', JSON.stringify(globalAppParametersMap.value))
    }
  } else {
    globalAppParametersMap.value = {}
    localStorage.setItem('appParametersMap', JSON.stringify(globalAppParametersMap.value))
  }

  // 初始化 currentAppKey
  const savedCurrentAppKey = localStorage.getItem('currentAppKey')
  const appNames = Object.keys(globalAppKeyMap.value)

  if (savedCurrentAppKey && globalAppKeyMap.value[savedCurrentAppKey]) {
    globalCurrentAppKey.value = savedCurrentAppKey
  } else {
    // 使用第一个应用作为默认值
    globalCurrentAppKey.value = appNames[0] || ''
    if (globalCurrentAppKey.value) {
      localStorage.setItem('currentAppKey', globalCurrentAppKey.value)
    }
  }

  // 监听 currentAppKey 变化，自动保存到 localStorage
  watch(globalCurrentAppKey, (newValue) => {
    if (newValue) {
      localStorage.setItem('currentAppKey', newValue)
    }
  })

  // 监听 appKeyMap 变化，自动保存到 localStorage
  watch(
    globalAppKeyMap,
    (newValue) => {
      localStorage.setItem('appKeyMap', JSON.stringify(newValue))
    },
    { deep: true }
  )

  // 监听 appParametersMap 变化，自动保存到 localStorage
  watch(
    globalAppParametersMap,
    (newValue) => {
      localStorage.setItem('appParametersMap', JSON.stringify(newValue))
    },
    { deep: true }
  )
}
