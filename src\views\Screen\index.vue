<!--
 * @Description: 大屏主页面
 * @Autor: qian
 * @Date: 2023-06-19 17:04:41
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-26 15:54:36
-->
<template>
  <div class="Screen" id="ScreenBox">
    <!-- <div id="player" v-show="show3DModel"></div> -->

    <div class="screen-movable-area">
      <!-- 使用 transition 组件为 ScreenLeft 添加动画 -->
      <transition name="screen-left-fade">
        <ScreenLeft class="left-pannel backdrop-filter backdrop-blur-30" v-if="isShowLeft" :class="{ show: rotationLeft }" />
      </transition>

      <div class="screen-movable-area-main" id="popUpRoot">
        <OtherButtons :clearSelected="!rotationLeft && !rotationRight" :showModel="show3DModel" @btn-click="handleBtnClick" />

        <ScreenMiddlePage></ScreenMiddlePage>
      </div>

    </div>
    <AIRobot class="absolute  z-1000" />

  </div>
</template>

<script>
  export default {
    name: 'Screen'
  }
</script>
<script setup>
  import moment from 'moment'

  import SelectViewPort from './components/selectViewPort.vue'
  import ScreenLeft from './ScreenLeft/index.vue'
  import ScreenMiddlePage from './ScreenMiddlePage/index.vue'
  import ScreenRight from './ScreenRight/index.vue'
  import ScreenTop from './ScreenTop/index.vue'
  import AiButtons from './components/AiButtons/index.vue'
  import AIRobot from '@/components/AIRobot/index.vue'

  import useStore from '@/store/index.js'
  import lib from '@/utils/lib.ts'
  import OtherButtons from './components/OtherButtons/index.vue'
  
  // import { initLoad } from '@/utils/webRtcVideo.js'

  const { storeDictionary } = useStore()
  const showBothSide = ref(true)
  const isShowLeft = ref(true)
  const isShowRight = ref(true)
  const rotationLeft = ref(false)
  const rotationRight = ref(false)
  const show3DModel = ref(true)


  setTimeout(() => {
    isShowLeft.value = true
    isShowRight.value = true
  }, 300)
  const handleBtnClick = (val) => {
    console.log('handleBtnClick', val)
    rotationLeft.value = val
    rotationRight.value = val
  }
  const handleShowBothSide = (val) => {
    console.log('handleShowBothSide', val)
    isShowLeft.value = val
    isShowRight.value = val
    rotationLeft.value = false
    rotationRight.value = false
  }

  lib.provideTools.handleShowBothSide.provide(handleShowBothSide)
  // lib.provideTools.clickModel.provide(lib.ue().clickModel)

  // 关闭 清空 大图预览
  const closeViewer = () => {
    lib.store().storeDictionary.DIALOG_IMG([])
  }
  // 清除撒点
  const clearAllPoint = () => {
    lib.popWindow.removeDialog()
  }
  // const modelLoaded = ref(false)

  const isRoam = ref(false)

  onMounted(() => {
    console.log('onMounted')

    lib.store().storeDictionary.setDictMap()
    // initLoad()
    // initUE()
  })

  const leftMargin = ref(0)
  const rightMargin = ref(0)

  // 监听 rotationLeft 和 rotationRight 的变化
  watch(
    () => [rotationLeft.value, rotationRight.value],
    ([newLeft, newRight]) => {
      leftMargin.value = newLeft ? -183 : 0
      rightMargin.value = newRight ? -173 : 0
    }
  )
  // const { appContext } = getCurrentInstance()

  // lib.popWindow.createPopWindow(
  //         './Components/Test/index.vue',
  //         // './Components/StructureFile/index.vue',
  //         {
  //           left: 1420,
  //           top: 580,
  //           tag: 'structureWindow',
  //           appContext,
  //           appendParent: 'body',
  //           closeFunc: () => {
  //           }
  //         },
  //         {

  //         }
  //       )
</script>
<style lang="scss" scoped>
  .Screen {
    position: relative;
    width: $screen-width;
    height: $screen-height;
    overflow: hidden;

    // background: #04112031;
    .screen-movable-area {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      display: flex;
      pointer-events: none;
      .screen-movable-area-main {
        position: relative;
        flex: 1;
        height: 0;
        pointer-events: auto;
        background: red;
        &.tilt-exit {
          transition: all 0.5s ease-out;
        }
        &.tilt-enter {
          transition: all 0.5s ease-in;
        }
      }
      .left-pannel {
        transition: all 0.5s;
        &.show {
          // margin-left: 33px;
          // 距离 z=0 平面 800 像素,往内推 5 度
          transform: perspective(800px) rotateY(5deg);
          transform-origin: left;
        }
      }
      .right-pannel {
        transition: all 0.5s;
        &.show {
          // margin-right: 33px;
          transform: perspective(800px) rotateY(-5deg);
          transform-origin: right;
        }
      }
      .switch-model {
        position: absolute;
        top: 1250px;
        left: 40px;
        z-index: 999;
        width: 244px;
        height: 64px;
        padding-left: 82px;
        font-family: YouSheBiaoTiHei;
        font-size: 33px;
        font-weight: 400;
        line-height: 68px;
        color: #ffffff;
        cursor: pointer;
        background-image: url('@/assets/ScreenMiddle/switchModel.png');
        background-size: cover;
        &::before {
          position: absolute;
          top: 19px;
          left: 43px;
          width: 26px;
          height: 28px;
          content: '';
          background-image: url('@/assets/ScreenMiddle/switchModelIIcon.png');
          background-size: cover;
        }
      }
      .switch-clear {
        position: absolute;
        top: 1165px;
        left: 40px;
        z-index: 999;
        width: 244px;
        height: 64px;
        padding-left: 82px;
        font-family: YouSheBiaoTiHei;
        font-size: 33px;
        font-weight: 400;
        line-height: 68px;
        color: #ffffff;
        cursor: pointer;
        background-image: url('@/assets/ScreenMiddle/switchModel.png');
        background-size: cover;
        &::before {
          position: absolute;
          top: 19px;
          left: 43px;
          width: 26px;
          height: 28px;
          content: '';
          background-image: url('@/assets/ScreenMiddle/clearPoint.png');
          background-size: cover;
        }
      }
      .button {
        z-index: 999;
        width: 103px;
        height: 35px;
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        line-height: 30px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        background: #4b6f86;
        border: 3px solid #1161ab;
        border-radius: 18px;
        &.selected {
          color: #ffffff;
          background: #82b6f0;
        }
      }
      .switch-weather {
        position: absolute;
        top: 1008px;
        right: 20px;
      }
      .switch-views {
        position: absolute;
        top: 1058px;
        right: 20px;
      }
      .switch-car {
        position: absolute;
        top: 1108px;
        right: 20px;
      }
      .switch-follow {
        position: absolute;
        top: 1158px;
        right: 20px;
      }
      .switch-roam {
        position: absolute;
        top: 1209px;
        right: 20px;
      }
    }
    .show-hide-btn {
      position: absolute;
      top: 464px;
      width: 24px;
      height: 80px;
      cursor: pointer;
      &.show-hide-btn-left {
        left: 0;
      }
      &.show-hide-btn-right {
        right: 0;
      }
    }
  }
</style>
<style>
  .el-switch.is-checked .el-switch__core {
    background-color: #4b6f86;
  }
  .el-switch__core {
    min-width: 103px;
    height: 35px;

    /* border: 2px solid #00d1ff; */
    border: 3px solid #1161ab;
    border-radius: 18px;
  }
  .el-switch.is-checked .el-switch__core .el-switch__action {
    left: calc(100% - 26px);
    width: 21px;
    height: 21px;
  }
  .el-switch__core .el-switch__inner .is-text {
    font-family: 'Alibaba PuHuiTi';
    font-size: 20px;
  }
  #player,
  #streamingVideo {
    position: absolute;
    z-index: 0;
    width: 5120px;
    height: 1440px;
  }
</style>

<style lang="scss" scoped>
  .screen-left-fade-enter-active,
  .screen-left-fade-leave-active {
    transition: all 1s ease;
  }
  .screen-left-fade-enter-from,
  .screen-left-fade-leave-to {
    opacity: 0;
    transform: translateX(-100%);
  }
  .screen-right-fade-enter-active,
  .screen-right-fade-leave-active {
    transition: all 1s ease;
  }
  .screen-right-fade-enter-from,
  .screen-right-fade-leave-to {
    opacity: 0;
    transform: translateX(100%);
  }
</style>
