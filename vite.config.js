import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import { fileURLToPath, URL } from 'node:url'
import UnoCss from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig, loadEnv } from 'vite'
import basicSsl from '@vitejs/plugin-basic-ssl';

// import { viteExternalsPlugin } from 'vite-plugin-externals'
// import htmlConfig from 'vite-plugin-html-config'

// https://vitejs.dev/config/
export default ({ mode: VITE_MODE }) => {
  const env = loadEnv(VITE_MODE, process.cwd())

  const plugins = [
    vue(),
    vueJsx(),
    AutoImport({
      imports: ['vue']
    }),
    codeInspectorPlugin({
      bundler: 'vite'
    }),
    UnoCss(),
    basicSsl()
  ]

  // const externalConfig = viteExternalsPlugin({
  //   cesium: 'Cesium'
  // })
  // const htmlConfigs = htmlConfig({
  //   headScripts: [
  //     {
  //       src: '/lib/cesium/Cesium.js'
  //     }
  //   ],
  //   links: [
  //     {
  //       // ******* Always use external CesiumJS's style util official package output. *******
  //       // ******* 直到官方 CesiumJS 包导出样式文件前，都使用外部样式 *******
  //       rel: 'stylesheet',
  //       href: '/lib/cesium/Widgets/widgets.css'
  //     }
  //   ]
  // })
  // plugins.push(externalConfig, htmlConfigs)

  return defineConfig({
    root: './',
    base: `/${env.VITE_BASE}/`,
    build: {
      outDir: `./dist/${env.VITE_BASE}`,
      assetsDir: './static/',
      minify: !['false'].includes(env.VITE_IS_MINIFY),
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
        }
      }
    },
    plugins: plugins,
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      }
    },
    define: {
      _ISUE_: true
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "./src/style/var.scss";'
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: Number(env.VITE_APP_PORT),
      open: true,
      https: false
    }
  })
}
