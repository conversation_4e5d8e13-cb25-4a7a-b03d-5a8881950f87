<template>
	<div class="voice-button-container" :class="{ 'recording-mode': isRecording }">
		<!-- 语音按钮 - 只在未录音时显示 -->
		<div class="voice-button-wrapper" v-if="!isRecording">
			<el-button :class="['voice-button', { connected: isConnected }]" @click="toggleVoiceInput" circle size="large">
				<!-- 按钮图标 -->
				<el-icon size="24px" class="button-icon">
					<Microphone />
				</el-icon>
			</el-button>
		</div>

		<!-- Siri波形效果 - 录音时全屏显示 -->
		<div class="siri-waves" v-if="isRecording">
			<svg viewBox="0 0 200 100" class="wave-svg">
				<!-- 多条波形曲线 -->
				<path v-for="(wave, index) in waves" :key="index" :d="wave.path" :class="['wave-path', `wave-${index}`]" :style="{
					stroke: wave.color,
					strokeWidth: wave.width,
					opacity: wave.opacity,
					transform: `scaleY(${wave.scale})`
				}" />

				<!-- 中心圆点 -->
				<circle cx="100" cy="50" :r="centerDotRadius" :class="['center-dot']" :style="{
					opacity: centerDotOpacity,
					transform: `scale(${centerDotScale})`
				}" />
			</svg>
		</div>

		<!-- 关闭语音按钮 - 录音时显示 -->
		<div class="close-voice-button-wrapper" v-if="isRecording">
			<el-button class="close-voice-button" @click="stopVoiceInput" circle size="large" type="danger">
				<el-icon size="20px">
					<Close />
				</el-icon>
			</el-button>
		</div>

		<!-- 唤醒状态UI - 重新设计 -->
		<div class="wake-status-modern" v-if="isConnected && isRecording">
			<div class="wake-indicator" :class="{ 'wake-active': isWakeup }">
				<div class="wake-dot"></div>
				<div class="wake-text">
					{{ isWakeup ? '正在聆听...' : `说出"${wakeupWord}"来唤醒` }}
				</div>
			</div>
		</div>
	</div>
</template>



<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Microphone, Loading, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface VoiceButtonProps {
	wakeupWord?: string
	serverUrl?: string
	showWakeupWord?: boolean
	autoExitWakeup?: boolean // 是否在发送完一次后自动退出唤醒状态
}

const props = withDefaults(defineProps<VoiceButtonProps>(), {
	wakeupWord: window.config.wakeWord,
	serverUrl: window.config.voiceServiceBaseUrl,
	showWakeupWord: true,
	autoExitWakeup: true
})

const emit = defineEmits<{
	transcription: [text: string, timestamp: number]
	wakeupChanged: [isWakeup: boolean]
	connectionChanged: [isConnected: boolean]
}>()

// 状态管理
const isRecording = ref(false)
const isConnected = ref(false)
const isWakeup = ref(false)
const volumePercentage = ref(0)

// Siri波形特效相关状态
const waves = ref([])
const centerDotRadius = ref(2)
const centerDotOpacity = ref(0.8)
const centerDotScale = ref(1)

// 波形动画参数
const waveAnimationId = ref(null)
const time = ref(0)

// WebSocket 和音频相关
let ws: WebSocket | null = null
let audioContext: AudioContext | null = null
let analyser: AnalyserNode | null = null
let microphone: MediaStreamAudioSourceNode | null = null
let processor: ScriptProcessorNode | null = null

// VAD 配置
const VAD_CONFIG = {
	energyThreshold: 0.01,
	silenceFrames: 0,
	maxSilenceFrames: 4,
	minBufferSeconds: 0.5,
	maxBufferSeconds: 10,
	sampleRate: 16000,
	sampleBufferSize: 2048,
	isSpeechActive: false,
	audioBuffer: [] as number[],
	calibrating: true,
	calibrationFrames: 20,
	calibrationEnergies: [] as number[],
	wakeupSilenceFrames: 0,
	maxWakeupSilenceFrames: 24,
}

// 连接 WebSocket
const connectWebSocket = async () => {
	if (ws && ws.readyState === WebSocket.OPEN) {
		console.log('WebSocket 已连接')
		return true
	}

	try {
		ws = new WebSocket(props.serverUrl)

		return new Promise((resolve, reject) => {
			ws.onopen = () => {
				console.log('WebSocket 连接已建立')
				isConnected.value = true
				emit('connectionChanged', true)

				// 发送唤醒词作为热词
				sendHotwords([props.wakeupWord])
				ElMessage.success('语音服务连接成功')
				resolve(true)
			}

			ws.onmessage = (event) => {
				try {
					const data = JSON.parse(event.data)
					console.log('收到消息:', data)

					if (data.type === 'transcription') {
						// 唤醒逻辑
						if (!isWakeup.value && typeof data.text === 'string' && data.text.includes(props.wakeupWord)) {
							isWakeup.value = true
							emit('wakeupChanged', true)
							console.log(`🚀 唤醒成功: ${props.wakeupWord}`)
							return
						}

						// 退出唤醒逻辑
						if (isWakeup.value && typeof data.text === 'string' && (data.text.includes('退出唤醒') || data.text.includes('退出，唤醒'))) {
							isWakeup.value = false
							emit('wakeupChanged', false)
							VAD_CONFIG.wakeupSilenceFrames = 0
							console.log('🛌 检测到"退出唤醒"指令，已退出唤醒状态')
							return
						}

						// 唤醒状态下才发送识别结果
						if (isWakeup.value) {
							emit('transcription', data.text, data.timestamp)
							
							// 如果开启了自动退出唤醒状态，发送完一次后退出唤醒
							if (props.autoExitWakeup && data.text && data.text.trim().length > 0) {
								isWakeup.value = false
								emit('wakeupChanged', false)
								VAD_CONFIG.wakeupSilenceFrames = 0
								console.log('🔄 自动退出唤醒状态（发送完成后）')
							}
						} else {
							console.log('⏸ 未唤醒，忽略识别结果')
						}
					} else if (data.type === 'error') {
						ElMessage.error(`语音识别错误: ${data.message}`)
					}
				} catch (e) {
					console.error('解析消息失败:', e)
				}
			}

			ws.onclose = () => {
				console.log('WebSocket 连接已关闭')
				isConnected.value = false
				emit('connectionChanged', false)

				if (isRecording.value) {
					stopRecording()
				}
			}

			ws.onerror = (error) => {
				console.error('WebSocket 错误:', error)
				isConnected.value = false
				emit('connectionChanged', false)
				ElMessage.error('语音服务连接失败')
				reject(error)
			}
		})
	} catch (error) {
		console.error('连接失败:', error)
		ElMessage.error('语音服务连接失败')
		return false
	}
}

// 断开 WebSocket
const disconnectWebSocket = () => {
	if (ws) {
		ws.close()
		ws = null
	}

	if (isRecording.value) {
		console.log('正在录音，先停止录音')
		isRecording.value = false
		stopRecording()
	}

	isConnected.value = false
	isWakeup.value = false
	VAD_CONFIG.wakeupSilenceFrames = 0
	emit('connectionChanged', false)
	emit('wakeupChanged', false)
	ElMessage.info('语音服务已断开')
}

// 发送热词
const sendHotwords = (hotwords: string[]) => {
	if (!ws || ws.readyState !== WebSocket.OPEN) {
		console.log('WebSocket 未连接，无法发送热词')
		return
	}

	const message = {
		type: 'update_hotwords',
		hotwords: hotwords
	}

	ws.send(JSON.stringify(message))
	console.log('发送热词更新:', hotwords)
}

// 计算音频能量
const calculateEnergy = (audioData: Float32Array): number => {
	let sum = 0
	for (let i = 0; i < audioData.length; i++) {
		sum += audioData[i] * audioData[i]
	}
	return Math.sqrt(sum / audioData.length)
}

// VAD 检测
const detectVAD = (audioData: Float32Array) => {
	const energy = calculateEnergy(audioData)
	const isSpeech = energy > VAD_CONFIG.energyThreshold

	return {
		isSpeech: isSpeech,
		energy: energy
	}
}

// 发送音频数据到服务器
const sendAudioToServer = (audioData: Float32Array) => {
	if (ws && ws.readyState === WebSocket.OPEN) {
		// 转换为 Int16Array
		const int16Data = new Int16Array(audioData.length)
		for (let i = 0; i < audioData.length; i++) {
			int16Data[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768))
		}
		ws.send(int16Data.buffer)
	}
}

// 处理音频块
const processAudioChunk = (audioData: Float32Array) => {
	// 校准阶段
	if (VAD_CONFIG.calibrating) {
		const energy = calculateEnergy(audioData)
		VAD_CONFIG.calibrationEnergies.push(energy)

		if (VAD_CONFIG.calibrationEnergies.length >= VAD_CONFIG.calibrationFrames) {
			const sum = VAD_CONFIG.calibrationEnergies.reduce((a, b) => a + b, 0)
			const avg = sum / VAD_CONFIG.calibrationEnergies.length
			VAD_CONFIG.energyThreshold = avg * 1.5
			VAD_CONFIG.calibrating = false
			console.log(`✅ 动态能量阈值校准完成: ${VAD_CONFIG.energyThreshold.toFixed(4)}`)
		}
		return
	}

	const vadResult = detectVAD(audioData)
	const isSpeech = vadResult.isSpeech

	if (isSpeech) {
		// 检测到语音
		VAD_CONFIG.silenceFrames = 0
		VAD_CONFIG.wakeupSilenceFrames = 0

		if (!VAD_CONFIG.isSpeechActive) {
			VAD_CONFIG.isSpeechActive = true
			VAD_CONFIG.audioBuffer = []
			console.log('🎙️ 开始语音活动')
		}

		// 添加到缓冲区
		VAD_CONFIG.audioBuffer.push(...Array.from(audioData))

		// 检查缓冲区大小
		const maxBufferSize = VAD_CONFIG.maxBufferSeconds * VAD_CONFIG.sampleRate
		if (VAD_CONFIG.audioBuffer.length > maxBufferSize) {
			VAD_CONFIG.audioBuffer = VAD_CONFIG.audioBuffer.slice(-maxBufferSize)
		}
	} else {
		// 静音
		VAD_CONFIG.silenceFrames++

		// 唤醒静音帧计数
		if (isWakeup.value) {
			VAD_CONFIG.wakeupSilenceFrames++
			if (VAD_CONFIG.wakeupSilenceFrames >= VAD_CONFIG.maxWakeupSilenceFrames) {
				isWakeup.value = false
				emit('wakeupChanged', false)
				VAD_CONFIG.wakeupSilenceFrames = 0
				console.log('🛌 唤醒静音帧超限，退出唤醒状态')
			}
		}

		if (VAD_CONFIG.isSpeechActive) {
			VAD_CONFIG.audioBuffer.push(...Array.from(audioData))

			// 检查是否达到静音阈值
			if (VAD_CONFIG.silenceFrames >= VAD_CONFIG.maxSilenceFrames) {
				const minBufferSize = VAD_CONFIG.minBufferSeconds * VAD_CONFIG.sampleRate

				if (VAD_CONFIG.audioBuffer.length >= minBufferSize) {
					const audioToSend = new Float32Array(VAD_CONFIG.audioBuffer)
					sendAudioToServer(audioToSend)
					console.log(`🧹 发送音频片段 ${VAD_CONFIG.audioBuffer.length} 样本`)
				}

				// 重置状态
				VAD_CONFIG.audioBuffer = []
				VAD_CONFIG.isSpeechActive = false
			}
		}
	}
}

// 生成波形路径
const generateWavePath = (frequency, amplitude, phase, points = 40) => {
	let path = `M 20 50`

	for (let i = 0; i <= points; i++) {
		const x = 20 + (160 * i) / points
		const y = 50 + amplitude * Math.sin((i * frequency + phase) * 0.15) * Math.sin(i * 0.08)
		path += ` L ${x} ${y}`
	}

	return path
}

// 初始化波形
const initWaves = () => {
	waves.value = [
		{
			path: generateWavePath(2, 0, 0),
			color: '#4FC3F7',
			width: 1,
			opacity: 0.9,
			scale: 1,
			frequency: 2,
			baseAmplitude: 12,
			phase: 0
		},
		{
			path: generateWavePath(3, 0, 0.5),
			color: '#29B6F6',
			width: 1,
			opacity: 0.7,
			scale: 1,
			frequency: 3,
			baseAmplitude: 9,
			phase: 0.5
		},
		{
			path: generateWavePath(4, 0, 1),
			color: '#03A9F4',
			width: 1,
			opacity: 0.6,
			scale: 1,
			frequency: 4,
			baseAmplitude: 6,
			phase: 1
		},
		{
			path: generateWavePath(1.5, 0, 1.5),
			color: '#0288D1',
			width: 1,
			opacity: 0.8,
			scale: 1,
			frequency: 1.5,
			baseAmplitude: 15,
			phase: 1.5
		},
		{
			path: generateWavePath(5, 0, 2),
			color: '#0277BD',
			width: 1,
			opacity: 0.5,
			scale: 1,
			frequency: 5,
			baseAmplitude: 4,
			phase: 2
		}
	]
}

// 更新波形动画
const updateWaves = () => {
	if (!isRecording.value) return

	time.value += 0.05
	const volumeMultiplier = Math.max(0.05, volumePercentage.value / 100)
	const breatheEffect = 0.7 + Math.sin(time.value * 0.8) * 0.3

	// 更新每条波形
	waves.value.forEach((wave, index) => {
		const amplitude = wave.baseAmplitude * volumeMultiplier * breatheEffect
		const currentPhase = wave.phase + time.value * (0.5 + index * 0.1)

		wave.path = generateWavePath(wave.frequency, amplitude, currentPhase)
		wave.scale = 0.6 + volumeMultiplier * 0.8 + Math.sin(time.value * 0.6 + index) * 0.2

		// 呼吸效果的透明度变化
		const baseOpacity = [0.9, 0.7, 0.6, 0.8, 0.5][index]
		wave.opacity = baseOpacity * (0.4 + volumeMultiplier * 0.6) * (0.8 + Math.sin(time.value * 0.7 + index * 0.5) * 0.2)
	})

	// 更新中心圆点
	centerDotRadius.value = 1.5 + volumeMultiplier * 4
	centerDotScale.value = 0.7 + volumeMultiplier * 0.8 + Math.sin(time.value * 0.9) * 0.3
	centerDotOpacity.value = 0.7 + volumeMultiplier * 0.3 + Math.sin(time.value * 0.5) * 0.2

	waveAnimationId.value = requestAnimationFrame(updateWaves)
}

// 开始波形动画
const startWaveAnimation = () => {
	if (waveAnimationId.value) {
		cancelAnimationFrame(waveAnimationId.value)
	}
	time.value = 0
	initWaves()
	updateWaves()
}

// 停止波形动画
const stopWaveAnimation = () => {
	if (waveAnimationId.value) {
		cancelAnimationFrame(waveAnimationId.value)
		waveAnimationId.value = null
	}
}

// 切换语音输入状态（包含连接和录音）
const toggleVoiceInput = async () => {
	console.log('切换语音输入状态', isRecording.value, isConnected.value)
	if (!isConnected.value) {
		// 如果未连接，先连接然后自动开始录音
		const connected = await connectWebSocket()
		if (connected) {
			await startRecording()
		}
	} else {
		// 如果已连接，则切换录音状态或断开连接
		if (isRecording.value) {
			// 正在录音，停止录音并断开连接
			console.log('正在录音，先停止录音')
			stopRecording()
			disconnectWebSocket()
		} else {
			// 已连接但未录音，开始录音
			await startRecording()
		}
	}
}

// 停止语音输入（停止录音并断开连接）
const stopVoiceInput = () => {
	console.log('停止语音输入', isRecording.value)
	if (isRecording.value) {
		stopRecording()
	}
	disconnectWebSocket()
}

// 开始录音
const startRecording = async () => {
	try {
		// 重置 VAD 校准状态
		VAD_CONFIG.calibrating = true
		VAD_CONFIG.calibrationEnergies = []

		const stream = await navigator.mediaDevices.getUserMedia({
			audio: {
				sampleRate: 16000,
				channelCount: 1,
				echoCancellation: true,
				noiseSuppression: true,
				autoGainControl: false
			}
		})

		// 设置音频上下文
		audioContext = new AudioContext({ sampleRate: VAD_CONFIG.sampleRate })
		microphone = audioContext.createMediaStreamSource(stream)

		// 创建分析器用于音量显示
		analyser = audioContext.createAnalyser()
		analyser.fftSize = 256
		microphone.connect(analyser)

		// 创建处理器
		processor = audioContext.createScriptProcessor(VAD_CONFIG.sampleBufferSize, 1, 1)

		processor.onaudioprocess = (e) => {
			if (!isRecording.value) return

			const inputData = e.inputBuffer.getChannelData(0)

			// 计算音量
			let sum = 0
			for (let i = 0; i < inputData.length; i++) {
				sum += inputData[i] * inputData[i]
			}
			const rms = Math.sqrt(sum / inputData.length)
			volumePercentage.value = Math.min(100, rms * 100 * 10)

			// VAD 处理
			processAudioChunk(inputData)
		}

		microphone.connect(processor)
		processor.connect(audioContext.destination)

		isRecording.value = true
		startWaveAnimation() // 开始波形动画
		ElMessage.success('开始录音')

		// 开始波形动画
		startWaveAnimation()
	} catch (error) {
		console.error('录音启动失败:', error)
		const isHttps = window.location.protocol === 'https:'
		let errMsg = '录音启动失败，请检查麦克风权限'
		if (!isHttps) {
			errMsg += '（当前非 https 环境，部分浏览器可能不支持麦克风权限，请使用 https 访问）'
		}
		ElMessage.error(errMsg)
	}
}

// 停止录音
const stopRecording = () => {
	isRecording.value = false
	stopWaveAnimation() // 停止波形动画

	// 重置 VAD 状态
	VAD_CONFIG.silenceFrames = 0
	VAD_CONFIG.isSpeechActive = false
	VAD_CONFIG.audioBuffer = []

	if (processor) {
		processor.disconnect()
		processor.onaudioprocess = null
		processor = null
	}

	if (microphone) {
		microphone.disconnect()
		microphone = null
	}

	if (audioContext) {
		// 关闭所有关联的音频流
		if (audioContext.state !== 'closed') {
			audioContext.close()
		}
		audioContext = null
	}

	// 停止所有麦克风流轨道
	try {
		const tracks = (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) ? [] : undefined
		if (microphone && microphone.mediaStream) {
			microphone.mediaStream.getTracks().forEach(track => track.stop())
		}
	} catch (e) {
		// 忽略 stop 失败
		console.error('停止麦克风流失败:', e)
	}

	volumePercentage.value = 0
	ElMessage.info('停止录音')
	// ...existing code...
}

// 监听唤醒状态变化
watch(isWakeup, (newValue) => {
	console.log('唤醒状态变化:', newValue)
})

// 组件挂载
onMounted(() => {
	// 可以在这里自动连接
	// connectWebSocket()
})

// 组件卸载
onUnmounted(() => {
	if (isRecording.value) {
		stopRecording()
	}

	if (ws) {
		ws.close()
	}

	stopWaveAnimation() // 清理波形动画
})

// 暴露方法给父组件
defineExpose({
	toggleVoiceInput,
	isRecording,
	isConnected,
	isWakeup,
	autoExitWakeup: () => props.autoExitWakeup
})
</script>

<style scoped>
.voice-button-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 12px;
	transition: all 0.3s ease;
}

.voice-button-container.recording-mode {
	background: rgba(0, 0, 0, 0.75);
	backdrop-filter: blur(10px);
	border-radius: 20px;
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	z-index: 5;
}

.voice-button-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.voice-button {
	/* width: 60px;
	height: 60px; */
	background: rgba(255, 255, 255, 0.15) !important;
	backdrop-filter: blur(6px);
	border-radius: 50% !important;
	border: none !important;
	transition: all 0.3s ease;
	color: #222 !important;
	position: relative;
	overflow: hidden;
}

.voice-button:hover {
	background: rgba(67, 158, 253, 0.25) !important;
	box-shadow: 0 4px 16px 0 rgba(67, 158, 253, 0.18);
	color: #1976d2 !important;
}

.voice-button.connected {
	background: rgba(76, 175, 80, 0.25) !important;
	color: #2e7d32 !important;
}

.button-icon {
	position: relative;
	z-index: 2;
}

.siri-waves {
	position: absolute;
	top: -32px;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1;
}

.wave-svg {
	width: 100%;
	height: 100%;
	max-width: 100%;
	max-height: 100%;
}

.wave-path {
	fill: none;
	stroke-linecap: round;
	stroke-linejoin: round;
	transform-origin: center;
	transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	filter: drop-shadow(0 0 4px rgba(79, 195, 247, 0.6));
}

.wave-0 {
	animation: wave-breathe 2s ease-in-out infinite;
}

.wave-1 {
	animation: wave-breathe 2.2s ease-in-out infinite 0.2s;
}

.wave-2 {
	animation: wave-breathe 2.4s ease-in-out infinite 0.4s;
}

.wave-3 {
	animation: wave-breathe 1.8s ease-in-out infinite 0.6s;
}

.wave-4 {
	animation: wave-breathe 2.6s ease-in-out infinite 0.8s;
}

.center-dot {
	fill: #4FC3F7;
	transform-origin: center;
	transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
	filter: drop-shadow(0 0 8px rgba(79, 195, 247, 0.9));
	animation: dot-pulse 1.5s ease-in-out infinite;
}

/* 关闭语音按钮 */
.close-voice-button-wrapper {
	position: absolute;
	top: 20px;
	right: 20px;
	z-index: 10;
}

.close-voice-button {
	background: rgba(244, 67, 54, 0.2) !important;
	backdrop-filter: blur(6px);
	border: 1px solid rgba(244, 67, 54, 0.3) !important;
	color: #f44336 !important;
	transition: all 0.3s ease;
}

.close-voice-button:hover {
	background: rgba(244, 67, 54, 0.3) !important;
	box-shadow: 0 4px 16px 0 rgba(244, 67, 54, 0.3);
	transform: scale(1.05);
}

/* 新的唤醒状态UI */
.wake-status-modern {
	position: absolute;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 10;
}

.wake-indicator {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px 24px;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	border-radius: 25px;
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.wake-indicator.wake-active {
	background: rgba(76, 175, 80, 0.2);
	border-color: rgba(76, 175, 80, 0.4);
}

.wake-dot {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background: #ffa726;
	transition: all 0.3s ease;
	animation: wake-dot-pulse 1.5s ease-in-out infinite;
}

.wake-indicator.wake-active .wake-dot {
	background: #4caf50;
	animation: wake-dot-active 2s ease-in-out infinite;
}

.wake-text {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	font-weight: 500;
	white-space: nowrap;
}

@keyframes wake-dot-pulse {

	0%,
	100% {
		opacity: 0.6;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.2);
	}
}

@keyframes wake-dot-active {

	0%,
	100% {
		opacity: 1;
		transform: scale(1);
	}

	50% {
		opacity: 0.8;
		transform: scale(1.1);
	}
}

@keyframes wave-breathe {

	0%,
	100% {
		opacity: 0.4;
		transform: scaleY(0.6) scaleX(0.9);
	}

	50% {
		opacity: 1;
		transform: scaleY(1.4) scaleX(1.1);
	}
}

@keyframes dot-pulse {

	0%,
	100% {
		transform: scale(0.8);
		opacity: 0.6;
	}

	25% {
		transform: scale(1.2);
		opacity: 1;
	}

	75% {
		transform: scale(1.1);
		opacity: 0.9;
	}
}
</style>
