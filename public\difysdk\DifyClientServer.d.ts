export declare const BASE_URL = "https://api.dify.ai/v1";
export declare const routes: {
    application: {
        method: string;
        url: () => string;
    };
    feedback: {
        method: string;
        url: (message_id: string) => string;
    };
    createCompletionMessage: {
        method: string;
        url: () => string;
    };
    createChatMessage: {
        method: string;
        url: () => string;
    };
    getConversationMessages: {
        method: string;
        url: () => string;
    };
    getConversations: {
        method: string;
        url: () => string;
    };
    renameConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    deleteConversation: {
        method: string;
        url: (conversation_id: string) => string;
    };
    fileUpload: {
        method: string;
        url: () => string;
    };
    runWorkflow: {
        method: string;
        url: () => string;
    };
    audioToText: {
        method: string;
        url: () => string;
    };
};
/**
 * Base client for interacting with the Dify API
 * This class is designed to be used in Node.js environments
 * since exposing API keys in browsers is not secure
 */
export declare class DifyClientServer {
    protected apiKey: string;
    protected baseUrl: string;
    /**
     * Create a new Dify client
     *
     * @param apiKey - Your Dify API key
     * @param baseUrl - The Dify API base URL, defaults to https://api.dify.ai/v1
     */
    constructor(apiKey: string, baseUrl?: string);
    /**
     * Update the API key used by the client
     *
     * @param apiKey - Your new Dify API key
     */
    updateApiKey(apiKey: string): void;
    /**
     * Send a request to the Dify API
     *
     * @param method - HTTP method (GET, POST, etc.)
     * @param endpoint - API endpoint
     * @param data - Request payload for POST, PUT requests
     * @param params - Query parameters for the request
     * @param stream - Whether to request a streaming response
     * @param headerParams - Additional headers to include in the request
     * @returns Response from the Dify API
     */
    sendRequest(method: string, endpoint: string, data?: any, params?: any, stream?: boolean, headerParams?: Record<string, string>): Promise<any>;
    /**
     * Send feedback for a message
     *
     * @param message_id - ID of the message to provide feedback for
     * @param rating - Feedback rating ('like' or 'dislike')
     * @param user - User identifier
     * @returns Response from the Dify API
     */
    messageFeedback(message_id: string, rating: string, user: string): Promise<any>;
    /**
     * Get application parameters
     *
     * @param user - User identifier
     * @returns Application parameters from the Dify API
     */
    getApplicationParameters(user: string): Promise<any>;
    /**
     * Upload a file to the Dify API
     *
     * @param data - Form data containing the file to upload
     * @returns Response from the Dify API
     */
    fileUpload(data: any): Promise<any>;
    /**
     * Convert audio to text
     *
     * @param data - Form data containing the audio file
     * @returns Transcription response from the Dify API
     */
    audioToText(data: any): Promise<any>;
}
/**
 * Client for interacting with the Dify Completion API
 */
export declare class CompletionClientServer extends DifyClientServer {
    /**
     * Create a completion message
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param files - Files to include with the request
     * @returns Response from the Dify API
     */
    createCompletionMessage(inputs: Record<string, any>, user: string, stream?: boolean, files?: any): Promise<any>;
    /**
     * Run a workflow
     *
     * @param inputs - Input parameters for the workflow
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param files - Files to include with the request
     * @returns Response from the Dify API
     */
    runWorkflow(inputs: Record<string, any>, user: string, stream?: boolean, files?: any): Promise<any>;
}
/**
 * Client for interacting with the Dify Chat API
 */
export declare class ChatClientServer extends DifyClientServer {
    /**
     * Create a chat message
     *
     * @param inputs - Input parameters for the chat message
     * @param query - The user's message/query
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param conversation_id - ID of the conversation to add the message to
     * @param files - Files to include with the message
     * @returns Response from the Dify API
     */
    createChatMessage(inputs: Record<string, any>, query: string, user: string, stream?: boolean, conversation_id?: string | null, files?: any): Promise<any>;
    /**
     * Get messages from a conversation
     *
     * @param user - User identifier
     * @param conversation_id - ID of the conversation to get messages from
     * @param first_id - ID of the first message to get
     * @param limit - Maximum number of messages to get
     * @returns Chat messages from the Dify API
     */
    getConversationMessages(user: string, conversation_id?: string, first_id?: string | null, limit?: number | null): Promise<any>;
    /**
     * Get a list of conversations
     *
     * @param user - User identifier
     * @param first_id - ID of the first conversation to get
     * @param limit - Maximum number of conversations to get
     * @param pinned - Whether to only get pinned conversations
     * @returns List of conversations from the Dify API
     */
    getConversations(user: string, first_id?: string | null, limit?: number | null, pinned?: boolean | null): Promise<any>;
    /**
     * Rename a conversation
     *
     * @param conversation_id - ID of the conversation to rename
     * @param name - New name for the conversation
     * @param user - User identifier
     * @param auto_generate - Whether to auto-generate the name
     * @returns Response from the Dify API
     */
    renameConversation(conversation_id: string, name: string, user: string, auto_generate?: boolean): Promise<any>;
    /**
     * Delete a conversation
     *
     * @param conversation_id - ID of the conversation to delete
     * @param user - User identifier
     * @returns Response from the Dify API
     */
    deleteConversation(conversation_id: string, user: string): Promise<any>;
}
