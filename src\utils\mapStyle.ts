export const mapHomeStyle = [{
  'featureType': 'land',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on',
      'color': '#011325ff'
  }
}, {
  'featureType': 'water',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#051f31ff'
  }
}, {
  'featureType': 'building',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#001322ff'
  }
}, {
  'featureType': 'building',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#000109ff'
  }
}, {
  'featureType': 'water',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on',
      'color': '#103b4eff'
  }
}, {
  'featureType': 'village',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'town',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'district',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'country',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'city',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'continent',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'poilabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'poilabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'scenicspotslabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'scenicspotslabel',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#103b4eff'
  }
}, {
  'featureType': 'transportationlabel',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#103b4eff'
  }
}, {
  'featureType': 'transportationlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'airportlabel',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#103b4eff'
  }
}, {
  'featureType': 'airportlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'road',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#ffffffff'
  }
}, {
  'featureType': 'road',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#0b2125ff'
  }
}, {
  'featureType': 'road',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'weight': '4'
  }
}, {
  'featureType': 'green',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on',
      'color': '#1d3b46ff'
  }
}, {
  'featureType': 'scenicspots',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on',
      'color': '#051f31ff'
  }
}, {
  'featureType': 'scenicspots',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#103b4eff'
  }
}, {
  'featureType': 'scenicspots',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1f5563ff',
      'weight': '1'
  }
}, {
  'featureType': 'continent',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'country',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'city',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'city',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'scenicspotslabel',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1f5563ff',
      'weight': '1'
  }
}, {
  'featureType': 'airportlabel',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1f5563ff',
      'weight': '1'
  }
}, {
  'featureType': 'transportationlabel',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1f5563ff',
      'weight': '1'
  }
}, {
  'featureType': 'railway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'subway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'highwaysign',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'nationalwaysign',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'nationalwaysign',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'provincialwaysign',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'provincialwaysign',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'tertiarywaysign',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'tertiarywaysign',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'subwaylabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'subwaylabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '90'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1.4'
  }
}, {
  'featureType': 'shopping',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'scenicspots',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'on'
  }
}, {
  'featureType': 'scenicspotslabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'manmade',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'manmade',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'highwaysign',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'water',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d3b4600'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text',
  'stylers': {
      'fontsize': '24'
  }
}, {
  'featureType': 'highway',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'highway',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'highway',
  'elementType': 'geometry.stroke',
  'stylers': {
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'highway',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'highway',
  'elementType': 'geometry',
  'stylers': {
      'weight': '3'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'geometry.stroke',
  'stylers': {
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'geometry',
  'stylers': {
      'weight': '3'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'arterial',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'tertiaryway',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'fourlevelway',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#205e6fff'
  }
}, {
  'featureType': 'local',
  'elementType': 'geometry.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#205e6fff'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'arterial',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'tertiaryway',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#2a5685ff'
  }
}, {
  'featureType': 'fourlevelway',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#023b47ff'
  }
}, {
  'featureType': 'local',
  'elementType': 'geometry.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#023b47ff'
  }
}, {
  'featureType': 'local',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#103b4eff'
  }
}, {
  'featureType': 'local',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1f5563ff',
      'weight': '1'
  }
}, {
  'featureType': 'fourlevelway',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#103b4eff'
  }
}, {
  'featureType': 'tertiaryway',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'arterial',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'labels.text.fill',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'arterial',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'tertiaryway',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1d5f9eff',
      'weight': '1'
  }
}, {
  'featureType': 'fourlevelway',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'visibility': 'on',
      'color': '#1f5563ff',
      'weight': '1'
  }
}, {
  'featureType': 'fourlevelway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'weight': '1'
  }
}, {
  'featureType': 'tertiaryway',
  'elementType': 'geometry',
  'stylers': {
      'weight': '1'
  }
}, {
  'featureType': 'local',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'weight': '1'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'geometry',
  'stylers': {
      'weight': '3'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'geometry',
  'stylers': {
      'weight': '3'
  }
}, {
  'featureType': 'arterial',
  'elementType': 'geometry',
  'stylers': {
      'weight': '3'
  }
}, {
  'featureType': 'highway',
  'stylers': {
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'stylers': {
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'stylers': {
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'stylers': {
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'highway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'stylers': {
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'stylers': {
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'stylers': {
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'stylers': {
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'nationalway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'provincialway',
  'stylers': {
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '8-10'
  }
}, {
  'featureType': 'provincialway',
  'stylers': {
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '8-10'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '8-10'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '8-10'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '8-10'
  }
}, {
  'featureType': 'provincialway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '8-10'
  }
}, {
  'featureType': 'cityhighway',
  'stylers': {
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'stylers': {
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'stylers': {
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'stylers': {
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'cityhighway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-9'
  }
}, {
  'featureType': 'arterial',
  'stylers': {
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '9-9'
  }
}, {
  'featureType': 'arterial',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '9-9'
  }
}, {
  'featureType': 'arterial',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '9-9'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'on'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '6',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '7',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '8',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '9',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '10',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '11',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '12',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '13',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '14',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '15',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '16',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '17',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '18',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '19',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'road',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#ffffffff',
      'level': '20',
      'curZoomRegionId': '0',
      'curZoomRegion': '6-20'
  }
}, {
  'featureType': 'subway',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'education',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'subwaystation',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on'
  }
}, {
  'featureType': 'fourlevelway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'fourlevelway',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'local',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'local',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'scenicspotsway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'universityway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'vacationway',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'subway',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'roadarrow',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'footbridge',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'crosswalk',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'entertainmentlabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'entertainmentlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'estatelabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'estatelabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'businesstowerlabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'businesstowerlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'companylabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'companylabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'governmentlabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'governmentlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'restaurantlabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'restaurantlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'hotellabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'hotellabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'shoppinglabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'shoppinglabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'lifeservicelabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'lifeservicelabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'carservicelabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'carservicelabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'transportationlabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'financelabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'financelabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'town',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'districtlabel',
  'elementType': 'labels.text.fill',
  'stylers': {
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'educationlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'educationlabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'medicallabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'medicallabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'underpass',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'education',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'entertainment',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'estate',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'parkinglot',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'playground',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'transportation',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'transportation',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'building',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on'
  }
}, {
  'featureType': 'districtlabel',
  'elementType': 'labels.text.stroke',
  'stylers': {
      'color': '#1d5f9eff'
  }
}, {
  'featureType': 'parkingspace',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'city',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'districtlabel',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'districtlabel',
  'elementType': 'labels.icon',
  'stylers': {
      'visibility': 'on'
  }
}, {
  'featureType': 'continent',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'country',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'island',
  'elementType': 'labels',
  'stylers': {
      'visibility': 'off'
  }
}, {
  'featureType': 'boundary',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on'
  }
}, {
  'featureType': 'LANDDOMESTIC',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'on',
      'color': '#043a59ff'
  }
}, {
  'featureType': 'background',
  'elementType': 'geometry',
  'stylers': {
      'visibility': 'off'
  }
}]