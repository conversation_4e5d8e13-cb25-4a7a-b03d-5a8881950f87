<template>
  <div class="ai-chat-contaienr">
    <div class="absolute top-0 left-0 w-full h80 bg-transparent cursor-move drag-container"></div>
    <el-icon class="!absolute top-30 right-30 cursor-pointer" size="36px" @click="handleClose"><CloseBold /></el-icon>
    <BubbleList :list="list" max-height="800px">
      <template #header="{ item }">
        <Thinking
          v-if="item.thinkingStatus"
          :content="item.thinkingContent"
          :status="item.thinkingStatus"
          autoCollapse
          class="thinking-chain-warp"
          color="#333333"
          maxWidth="1700px"
          @change="handleThinkingChange" />
      </template>
      <!-- 自定义底部 -->
      <template #footer="{ item }">
        <div class="quote-list" v-if="item?.quoteList?.length > 0">
          <div class="quote-item" v-for="(quote, index) in item.quoteList" :key="index" @click="handleQuote(quote)">
            <div class="quote-item-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="quote-item-content">
              <div class="quote-item-title">{{ quote.fileName }}</div>
            </div>
          </div>
        </div>
        <div v-if="item.echartsJSONData" class="echarts-container">
          <ZnChart :key="item.key" :option="item.echartsJSONData" width="800px" height="600px" />
        </div>
      </template>
      <!-- <template #footer="{ item }">
        <div class="footer-container" v-if="item.role === 'ai'">
          <el-icon @click="handleOper('复制', item)"><DocumentCopy /></el-icon>
          <el-icon @click="handleOper('刷新', item)"><Refresh /></el-icon>
          <el-icon @click="handleOper('删除', item)"><Delete /></el-icon>
        </div>
      </template> -->
    </BubbleList>
    <!-- 欢迎页 -->
    <WelcomePage mt30 v-if="showWelcomePage" @question="handleQuestion"></WelcomePage>
    <Sender
      ref="refSender"
      class="absolute bottom-40 left-120 right-120 sender-style"
      v-model="inputValue"
      @submit="handleQuestion(null)"
      variant="updown"
      allow-speech
      clearable
      :input-style="{ color: '#1D1D1D', fontSize: '25px' }"
      style="overflow: hidden"
      :loading="submitLoading">
      <template #prefix>
        <div class="tools-btns">
          <div
            class="px-8 py-5 border border-solid border-#333333 rounded-8 hover:bg-#f5f5f5 c-#333333 transition-colors duration-300 cursor-pointer"
            :class="{ selected: selectedTools.includes(item) }"
            v-for="(item, index) in toolsBtns"
            :key="index"
            @click="handleSelectTools(item)">
            {{ item }}
          </div>
        </div>
      </template>
      <template #action-list>
        <div style="display: flex; gap: 8px; align-items: center">
          <el-button round color="rgba(255,255,255,0.74)" @click="handleClear">
            <el-icon size="22px" color="#000000"><Delete /></el-icon>
          </el-button>
          <el-button round color="rgba(255,255,255,0.74)" @click="handleRecord">
            <el-icon size="22px" color="#000000">
              <Microphone v-if="!recording" />
              <Loading v-else />
            </el-icon>
          </el-button>
          <el-button
            round
            color="rgba(255,255,255,0.74)"
            :loading="submitLoading"
            v-if="!submitLoading"
            @click="handleQuestion(null)"
            :disabled="inputValue.length === 0">
            <el-icon size="22px" color="#000000"><Promotion /></el-icon>
          </el-button>
          <el-button v-else type="danger" circle @click="handleCancel">
            <el-icon class="is-loaidng">
              <Loading />
            </el-icon>
          </el-button>
        </div>
      </template>
    </Sender>
  </div>
</template>

<script setup lang="ts">
  import { DocumentCopy, Refresh, Delete, Document } from '@element-plus/icons-vue'
  import WelcomePage from './components/WelcomePage/index.vue' // 引入WelcomePage组件 欢迎页
  import { ZnChart } from 'znyg-frontend-common'

  import lib from '@/utils/lib'
  import { useAiChat } from './helper'
  const props = withDefaults(defineProps<{ question: string }>(), { question: '' })
  const emits = defineEmits(['close'])

  const refSender = ref(null)

  const toolsBtns = ['知识库', 'DeepSeek']
  const selectedTools = ref('知识库')

  const showWelcomePage = ref(true) // 控制欢迎页的显示 默认显示

  const handleSelectTools = (item) => {
    selectedTools.value = item
    currentConversationId.value = ''
    if (item !== '业务库') {
      //非业务科，则需要携带最后的五条历史记录
      isCarryHistory.value = true
    }
  }

  const appKey = computed(() => {
    const appKeyMap = {
      业务库: 'chatdb',
      知识库: 'qjgfkf',
      DeepSeek: 'deepseekAndSearch',
      控制: 'BimEngineCode2'
    }
    return appKeyMap[selectedTools.value]
  })
  const {
    inputValue,
    recording,
    submitLoading,
    list,
    currentConversationId,
    isCarryHistory,
    handleRecord,
    handleCancel,
    handleSubmit,
    handleClear,
    handleThinkingChange
  } = useAiChat(appKey)

  const handleClose = () => {
    console.log('handleClose')
    emits('close')
  }

  const handleQuestion = (question) => {
    showWelcomePage.value = false // 点击问题后，隐藏欢迎页
    question && (inputValue.value = question)
    handleSubmit()
  }

  onMounted(async () => {
    console.log('初始化')
    const divParent = document.getElementById('ai-chat-container')
    const div = divParent.querySelector('.popWindow') as HTMLElement
    // document.addEventListener('mousemove', function (e) {
    //   const x = e.clientX - window.innerWidth / 2
    //   const y = e.clientY - window.innerHeight / 2
    //   const rotationX = (y / window.innerHeight) * -30
    //   const rotationY = (x / window.innerWidth) * 30
    //   div.style.transform = `rotateX(${rotationX}deg) rotateY(${rotationY}deg)`
    // })
    div.addEventListener('mousemove', (e: MouseEvent) => {
      const rect = div.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      // 元素的宽度和高度
      const width = rect.width
      const height = rect.height

      // 确定鼠标在哪个象限/角落
      let rotateX = 0
      let rotateY = 0
      const maxRotation = 5 // 进一步减小最大旋转角度

      // 计算缓冲区域（距离边缘100px）
      const buffer = 100
      const effectiveX = Math.max(buffer, Math.min(x, width - buffer))
      const effectiveY = Math.max(buffer, Math.min(y, height - buffer))

      // 计算每个方向的旋转强度（0-1之间）
      const xStrength = Math.abs((effectiveX / width) * 2 - 1) // 水平方向强度
      const yStrength = Math.abs((effectiveY / height) * 2 - 1) // 垂直方向强度

      // 左上角
      if (effectiveX < width / 2 && effectiveY < height / 2) {
        rotateX = maxRotation * (1 - effectiveY / (height / 2)) // 上边抬起
        rotateY = -maxRotation * (1 - effectiveX / (width / 2)) // 左边抬起
      }
      // 右上角
      else if (effectiveX >= width / 2 && effectiveY < height / 2) {
        rotateX = maxRotation * (1 - effectiveY / (height / 2)) // 上边抬起
        rotateY = maxRotation * ((effectiveX - width / 2) / (width / 2)) // 右边抬起
      }
      // 左下角
      else if (effectiveX < width / 2 && effectiveY >= height / 2) {
        rotateX = maxRotation * ((effectiveY - height / 2) / (height / 2)) // 下边抬起
        rotateY = -maxRotation * (1 - effectiveX / (width / 2)) // 左边抬起
      }
      // 右下角
      else {
        rotateX = maxRotation * ((effectiveY - height / 2) / (height / 2)) // 下边抬起
        rotateY = maxRotation * ((effectiveX - width / 2) / (width / 2)) // 右边抬起
      }

      // 应用平滑过渡
      div.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`
    })

    if (props.question) {
      showWelcomePage.value = false

      setTimeout(() => {
        handleQuestion(props.question)
      }, 1000)
    }
  })

  onUnmounted(() => {
    handleClear()
  })

  const handleOper = (type: '复制' | '刷新' | '删除', item: any) => {
    console.log(type, item)
  }

  const handleQuote = (quote) => {
    console.log(quote)
    window.open(quote.url, '_blank')
  }
</script>

<style scoped>
  .ai-chat-contaienr {
    position: absolute;
    width: 1600px;
    height: 1042px;
    padding: 80px 115px 47px 120px;
    overflow: hidden;

    /* background: rgb(255 255 255 / 70%); */

    /* backdrop-filter: blur(10px); */
    border-radius: 26px;
    box-shadow: inset 0 0 22px 1px rgb(255 255 255 / 73%);

    /* transition: transform 0.1s ease; */

    /* transform-style: preserve-3d; */
    .tools-btns {
      display: flex;
      gap: 10px;
      font-size: 20px;
      .selected {
        color: #0e1df0;
        border-color: #0e1df0;
      }
    }
  }

  /* 引用列表样式 */
  .quote-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
  }
  .quote-item {
    position: relative;
    display: flex;
    gap: 12px;
    align-items: flex-start;
    padding: 10px 14px;
    overflow: hidden;
    cursor: pointer;
    background: linear-gradient(135deg, rgb(255 255 255 / 90%) 0%, rgb(248 250 252 / 80%) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgb(226 232 240 / 60%);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 6%), 0 1px 3px rgb(0 0 0 / 10%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .quote-item::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 3px;
    content: '';
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .quote-item:hover {
    border-color: rgb(102 126 234 / 30%);
    box-shadow: 0 8px 25px rgb(0 0 0 / 12%), 0 4px 10px rgb(0 0 0 / 8%);
    transform: translateY(-2px);
  }
  .quote-item:hover::before {
    opacity: 1;
  }
  .quote-item-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    box-shadow: 0 2px 8px rgb(102 126 234 / 30%);
    transition: all 0.3s ease;
  }
  .quote-item:hover .quote-item-icon {
    box-shadow: 0 4px 12px rgb(102 126 234 / 40%);
    transform: scale(1.05);
  }
  .quote-item-content {
    flex: 1;
    min-width: 0;
  }
  .quote-item-title {
    display: -webkit-box;
    overflow: hidden;
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    color: #1e293b;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .quote-item-text {
    display: -webkit-box;
    overflow: hidden;
    font-size: 16px;
    line-height: 1.6;
    color: #64748b;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  /* 响应式设计 */
  @media (width <= 768px) {
    .quote-item {
      gap: 10px;
      padding: 12px;
    }
    .quote-item-icon {
      width: 36px;
      height: 36px;
    }
    .quote-item-title {
      font-size: 16px;
    }
    .quote-item-text {
      font-size: 14px;
    }
  }
</style>
<style lang="scss">
  .ai-chat-contaienr {
    .echarts-container {
      background: #ffffff;
    }
    .el-bubble-content {
      font-size: 25px !important;
      line-height: 36px !important;
      color: #242424 !important;
    }
    .el-bubble-end {
      --el-fill-color: #9fc6ff;
    }
    .sender-style {
      background: rgb(255 255 255 / 40%);

      // border: 1px solid rgb(112 112 112 / 50%);
      border-radius: 20px;
      box-shadow: inset 0 0 22px 1px rgb(255 255 255 / 73%);
      .el-sender {
        border: none !important;
      }
      .el-textarea__inner::placeholder {
        color: #333333;
      }
    }
  }
  .thinking-chain-warp {
    margin-bottom: 20px !important;
    .content pre,
    .label {
      font-size: 18px;
    }
  }
</style>
