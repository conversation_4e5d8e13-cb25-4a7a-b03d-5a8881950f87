
import { defineStore } from 'pinia'

const useUe = defineStore('ue', {
  state: () => ({
    finished: false, // 模型是否加载完成
    hightedList: [] // 存储已经高亮的模型
  }),
  getters: {},
  actions: {
    setFinished() {
      this.finished = true
    },
    /**
     * @description: 清除所有选中的模型
     */
    clearHightedModel() {
      const modelRenderList = {
        models: this.hightedList.map((_) => {
          return {
            modelCode: _.code,
            highlight: {
              // 非必填,有该字段则添加模型高亮，无则取消模型高亮
              hightlightColor: null // 高亮的颜色 - 必填
            }
          }
        })
      }
      this.hightedList = []
      toUe5('modelRender', modelRenderList)
    },
    /**
     * @description: 高亮模型，并把模型对象存到hightedList数组中
     * @param {*} obj   {
          "familyId": 13882390,
          "familyTypeId": 13882397,
          "geometryId": 13882415,
          "modelId": 15482854,
          "sceneId": 15482814,
          "materialGroupInfoId": 13882406,
          "originalId": "9787e649-1f97-455d-b0dd-b3bdba78b67b-0010f422",
          "name": "静力水准仪:静力水准仪:1111074",
          "weight": 1,
          "modelCode": "SHSPDD03CJJ0026202309"
      }
     */
    highlightModel(obj) {
      const modelRenderList = {
        models: [
          {
            modelCode: obj.code,
            highlight: {
              // 非必填,有该字段则添加模型高亮，无则取消模型高亮
              hightlightColor: [255, 64, 0, 255], // 高亮的颜色 - 必填
              lineRenderWidth: 2.0, // 线的宽度
              isDynamic: 0 // 是否是动态高亮
            },
            // color: [24, 147, 255, 0.7 * 255],
            hide: false
          }
        ]
      }
      if (!this.hightedList.map((_) => _.code).includes(obj.code)) {
        // 不存在,则高亮
        toUe5('modelRender', modelRenderList)
        this.hightedList.push(obj)
      }
    }
  }
})

export default useUe
