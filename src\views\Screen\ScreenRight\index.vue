<!--
 * @Author: wang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-29 14:08:53
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-26 17:31:48
 * @FilePath: \bigscreen-qj-web\src\views\Screen\ScreenRight\index.vue
 * @Description:
 *
-->
<template>
  <div class="ScreenRight" :class="[activePage]">
    <component :is="activePage"></component>
  </div>
</template>

<script>
  import HomePage from './Components/HomePage/index.vue'
  export default {
    name: 'ScreenRight',
    components: { HomePage }
  }
</script>
<script setup>
  import { computed } from 'vue'

  import useStore from '@/store'

  const { screen } = useStore()

  const activePage = computed(() => {
    if (screen.activePage === 'Monitor') {
      return ''
    }
    return screen.activePage
  })
</script>
