import { useXStream } from 'vue-element-plus-x'
import lib from '@/utils/lib'

export const useAiRobot = (appKey) => {
  const inputValue = ref('')
  const inputOrigin = ref('')
  const recording = ref(false)
  const submitLoading = ref(false)
  const reply = ref('')
  const currentConversationId = ref('')
  const showSender = ref(false)

  const { startStream, cancel, data, error, isLoading } = useXStream()

  /**录音 */
  const handleRecord = () => {
    recording.value = !recording.value
    console.log('正在录音', recording.value)
  }

  /**取消提交 */
  const handleCancel = () => {
    if (submitLoading.value) {
      cancel()
      submitLoading.value = false
    }
  }

  const handleSubmit = () => {
    console.log('提交')
    reply.value = ''
    submitLoading.value = true
    inputOrigin.value = inputValue.value
    startSSE(inputValue.value)
  }

  const startSSE = async (query) => {
    try {
      const response = await fetch(`${window.config.difyAPIBaseUrl}/chat-messages`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'text/event-stream' },
        body: JSON.stringify({
          app_key: appKey.value,
          query: query,
          response_mode: 'streaming',
          inputs: {},
          conversation_id: currentConversationId.value
        })
      })
      const readableStream = response.body!

      await startStream({ readableStream })
      submitLoading.value = false
      inputValue.value = ''

      //结束后保存会话信息
      if (currentConversationId.value === '') {
        getConversationIdFromServer()
      }
    } catch (err) {
      console.error('Fetch error:', err)
    }
  }

  /**获取当前会话的conversation_id */
  const getConversationIdFromServer = async () => {
    const chatClient = new window.DifyChatboxSDK.ChatClientWeb(window.config.difyAPIBaseUrl, appKey.value)

    const conversationList = await chatClient.getConversations()
    console.log(conversationList)

    // 上面创建的新的聊天 在 conversationList.data[0]
    if (conversationList && conversationList.data.length > 0) {
      const conversation = conversationList.data[0]
      console.log(conversation)
      currentConversationId.value = conversation.id
    }
  }

  const content = computed(() => {
    if (!data.value.length) return ''
    let text = ''
    for (let index = 0; index < data.value.length; index++) {
      const chunk = data.value[index].data
      if (chunk) {
        try {
          const parsedChunk = JSON.parse(chunk)
          if (parsedChunk.event === 'message') {
            text += parsedChunk.answer
          }
        } catch (error) {
          console.error('解析数据时出错:', error)
        }
      }
    }
    return text
  })

  watch(content, (newVal) => {
    if (newVal) {
      if (appKey.value === 'BimEngineCode2') {
        if (newVal.indexOf('text') > -1 && newVal.indexOf('code') > -1) {
          try {
            console.log(newVal, '解析')
            const message = JSON.parse(newVal)
            // if (message.text.startsWith('```javascript')) {
            //   message.text = message.text.slice(message.text.indexOf('```javascript') + '```javascript'.length, message.text.lastIndexOf('```')).trim()
            // }
            // const text = JSON.parse(message.text)
            const text = message.text

            reply.value = '已帮您' + inputOrigin.value

            let code = ''
            // 下面是改动的地方
            if (text.code && typeof text.code === 'string') {
              code = text.code
            } else {
              code = text
            }
            // lib.utils.executeCode(code, window.BimEngine, lib._engineController.viewer, lib._engineController.currentProject, lib)
            return
          } catch (error) {
            console.error('解析数据时出错:', error)
          }
        }
      }
      reply.value = newVal
    }
  })
  return { inputValue, recording, submitLoading, reply, showSender,currentConversationId, handleRecord, handleCancel, handleSubmit }
}
