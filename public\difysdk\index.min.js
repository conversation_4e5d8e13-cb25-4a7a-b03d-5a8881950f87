!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("axios")):"function"==typeof define&&define.amd?define(["exports","axios"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).DifyChatboxSDK={},e.axios)}(this,(function(e,t){"use strict";var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},n(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function o(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(o.prototype=t.prototype,new o)}var i=function(){return i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)};function s(e,t,n,o){return new(n||(n=Promise))((function(i,s){function r(e){try{u(o.next(e))}catch(e){s(e)}}function a(e){try{u(o.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(r,a)}u((o=o.apply(e,t||[])).next())}))}function r(e,t){var n,o,i,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},r=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return r.next=a(0),r.throw=a(1),r.return=a(2),"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(a){return function(u){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;r&&(r=0,a[0]&&(s=0)),s;)try{if(n=1,o&&(i=2&a[0]?o.return:a[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,a[1])).done)return i;switch(o=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,o=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],o=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}"function"==typeof SuppressedError&&SuppressedError;var a="/api",u=1e5,c="application/octet-stream",d={method:"GET",mode:"cors",credentials:"include",headers:new Headers({"Content-Type":"application/json"}),redirect:"follow"};var l=function(e,t,n,o,i,s,r,a,u,c,d){var l;if(!e.ok)throw new Error("Network response was not ok");var h,p=null===(l=e.body)||void 0===l?void 0:l.getReader(),f=new TextDecoder("utf-8"),m="",v=!0;!function e(){var l=!1;null==p||p.read().then((function(p){if(p.done)n&&n();else{var b=(m+=f.decode(p.value,{stream:!0})).split("\n");try{b.forEach((function(e){if(e.startsWith("data: ")){try{h=JSON.parse(e.substring(6))}catch(e){return void t("",v,{conversationId:null==h?void 0:h.conversation_id,messageId:null==h?void 0:h.message_id})}if(400===h.status||!h.event)return t("",!1,{conversationId:void 0,messageId:"",errorMessage:null==h?void 0:h.message,errorCode:null==h?void 0:h.code}),l=!0,void(null==n||n(!0));"message"===h.event||"agent_message"===h.event?(t(h.answer.replace(/\\u[0-9a-f]{4}/g,(function(e,t){return String.fromCharCode(parseInt(t,16))})),v,{conversationId:h.conversation_id,taskId:h.task_id,messageId:h.id}),v=!1):"agent_thought"===h.event?null==o||o(h):"message_file"===h.event?null==r||r(h):"message_end"===h.event?null==i||i(h):"message_replace"===h.event?null==s||s(h):"workflow_started"===h.event?null==a||a(h):"workflow_finished"===h.event?null==u||u(h):"node_started"===h.event?null==c||c(h):"node_finished"===h.event&&(null==d||d(h))}})),m=b[b.length-1]}catch(e){return t("",!1,{conversationId:void 0,messageId:"",errorMessage:"".concat(e)}),l=!0,void(null==n||n(!0))}l||e()}}))}()},h=function(e,t,n){return s(void 0,void 0,void 0,(function(){var o,i,s,u,c,h,p,f,m,v,b,g,y,C,w;return r(this,(function(r){return o=n.onData,i=n.onCompleted,s=n.onThought,u=n.onFile,c=n.onMessageEnd,h=n.onMessageReplace,p=n.onWorkflowStarted,f=n.onWorkflowFinished,m=n.onNodeStarted,v=n.onNodeFinished,b=n.onError,g=n.apiBaseUrl,y=Object.assign({},d,{method:"POST"},t),C="".concat(g||a).concat(e.startsWith("/")?e:"/".concat(e)),(w=y.body)&&(y.body=JSON.stringify(w)),[2,globalThis.fetch(C,y).then((function(e){if(/^(2|3)\d{2}$/.test(e.status))return l(e,(function(e,t,n){n.errorMessage?null==b||b(n.errorMessage,n.errorCode):null==o||o(e,t,n)}),(function(){null==i||i()}),s,c,h,u,p,f,m,v);e.json().then((function(e){null==b||b(e.message||"Server Error",e.code)})).catch((function(){null==b||b("Server Error")}))})).catch((function(e){null==b||b(e)}))]}))}))},p=function(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),function(e,t,n){var o=n.needAllResponseContent,i=n.apiBaseUrl,l=Object.assign({},d,t),h="".concat(i||a).concat(e.startsWith("/")?e:"/".concat(e)),p=l.method,f=l.params,m=l.body;if(("GET"===p||"DELETE"===p)&&f){var v=[];Object.keys(f).forEach((function(e){return v.push("".concat(e,"=").concat(encodeURIComponent(f[e])))})),-1===h.search(/\?/)?h+="?".concat(v.join("&")):h+="&".concat(v.join("&")),delete l.params}return m&&(l.body=JSON.stringify(m)),Promise.race([new Promise((function(e,t){setTimeout((function(){return t(new Error("request timeout"))}),u)})),new Promise((function(e,t){return globalThis.fetch(h,l).then((function(t){return s(void 0,void 0,void 0,(function(){var n,i,s,a;return r(this,(function(r){switch(r.label){case 0:if(n=t.clone(),/^(2|3)\d{2}$/.test(t.status))return[3,4];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,t.json()];case 2:return i=r.sent(),[2,Promise.reject(i)];case 3:return s=r.sent(),[2,Promise.reject(s)];case 4:return 204===t.status?[2,e({result:"success"})]:(a=l.headers.get("Content-type")===c?t.blob():t.json(),[2,e(o?n:a)])}}))}))})).catch((function(e){return s(void 0,void 0,void 0,(function(){return r(this,(function(n){return console.error(" baseFetch err ",e),[2,t(e)]}))}))}))}))])}(e,t,n)},f=function(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),p(e,Object.assign({},t,{method:"GET"}),n)},m=function(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),p(e,Object.assign({},t,{method:"POST"}),n)},v=function(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),p(e,Object.assign({},t,{method:"DELETE"}),n)},b=function(e,t,n){void 0===n&&(n={});var o=n.needAllResponseContent,i=n.apiBaseUrl,s="".concat(i||a).concat(e.startsWith("/")?e:"/".concat(e)),r={method:"POST",mode:"cors",credentials:"include",body:t};return Promise.race([new Promise((function(e,t){setTimeout((function(){t(new Error("request timeout"))}),u)})),new Promise((function(e,t){globalThis.fetch(s,r).then((function(t){var n=t.clone();if(!/^(2|3)\d{2}$/.test(t.status.toString()))try{return t.json().then((function(e){return Promise.reject(e)}))}catch(e){return Promise.reject(e)}return t.json().then((function(t){e(o?n:t)}))})).catch((function(e){t(e)}))}))])},g=function(){function e(e){var t=this;this.iframeReady=!1,this.currentConversationId=null,this.messageHandlers={},this.iframe=e,this.setupMessageListener(),this.sendToIframe=function(e,n){t.iframe.contentWindow?t.iframe.contentWindow.postMessage({source:"parent-app",type:e,data:n},"*"):console.error("Cannot access iframe content window")}}return e.prototype.setupMessageListener=function(){var e=this;window.addEventListener("message",(function(t){var n=t.data||{},o=n.source,i=n.type,s=n.data;if("dify-chatbox"===o){switch(i){case"ready":e.handleChatboxReady();break;case"conversation-completed":e.handleConversationCompleted(s);break;case"response-stream":e.handleResponseStream(s);break;case"user-message-sent":e.handleUserMessageSent(s);break;case"conversation-history":e.handleConversationHistory(s);break;case"error":e.handleError(s);break;case"feedback-updated":e.handleFeedbackUpdated(s)}i&&e.messageHandlers[i]&&e.messageHandlers[i](s)}}))},e.prototype.handleChatboxReady=function(){this.iframeReady=!0},e.prototype.handleConversationCompleted=function(e){console.log("Conversation completed:",e),this.currentConversationId=e.conversationId},e.prototype.handleResponseStream=function(e){console.log("Received response chunk:",e.isFirstChunk?"(First chunk)":"(continuation)")},e.prototype.handleUserMessageSent=function(e){console.log("User sent message:",e.message)},e.prototype.handleConversationHistory=function(e){console.log("Received conversation history:",e.history)},e.prototype.handleError=function(e){console.error("Chatbox error:",e.message,"Code:",e.code)},e.prototype.handleFeedbackUpdated=function(e){console.log("Feedback updated for message:",e.messageId,"Rating:",e.rating)},e.prototype.sendMessage=function(e){var t=e.query,n=e.files,o=e.inputs;this.iframeReady?this.sendToIframe("send-message",{message:t,files:n,inputs:o}):console.warn("Chatbox is not ready yet")},e.prototype.clearConversation=function(){this.iframeReady?(this.sendToIframe("clear-conversation",{}),this.currentConversationId=null):console.warn("Chatbox is not ready yet")},e.prototype.setInputs=function(e){this.iframeReady?this.sendToIframe("set-inputs",e):console.warn("Chatbox is not ready yet")},e.prototype.getConversationHistory=function(){this.iframeReady?this.sendToIframe("get-conversation-history",{}):console.warn("Chatbox is not ready yet")},e.prototype.getStatus=function(){this.iframeReady?this.sendToIframe("get-status",{}):console.warn("Chatbox is not ready yet")},e.prototype.setLanguage=function(e){this.iframeReady?this.sendToIframe("set-language",{language:e}):console.warn("Chatbox is not ready yet")},e.prototype.on=function(e,t){this.messageHandlers[e]=t},e}(),y=function(){function e(e){this.iframe=null,this.bubbleButton=null,this.bubbleWindow=null,this.isOpen=!1,this.communicationClient=null,this.initialPosition={x:20,y:20},this.dragOffset={x:0,y:0},this.isDragging=!1,this.options=i({CHATBOT_CONFIG_NAME:"difyChatbotConfig",BUBBLE_BUTTON_ID:"dify-chatbot-bubble-button",BUBBLE_WINDOW_ID:"dify-chatbot-bubble-window",draggable:!1,ICONS:{open:'<svg id="openIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n          <path fill-rule="evenodd" clip-rule="evenodd" d="M7.7586 2L16.2412 2C17.0462 1.99999 17.7105 1.99998 18.2517 2.04419C18.8138 2.09012 19.3305 2.18868 19.8159 2.43598C20.5685 2.81947 21.1804 3.43139 21.5639 4.18404C21.8112 4.66937 21.9098 5.18608 21.9557 5.74818C21.9999 6.28937 21.9999 6.95373 21.9999 7.7587L22 14.1376C22.0004 14.933 22.0007 15.5236 21.8636 16.0353C21.4937 17.4156 20.4155 18.4938 19.0352 18.8637C18.7277 18.9461 18.3917 18.9789 17.9999 18.9918L17.9999 20.371C18 20.6062 18 20.846 17.9822 21.0425C17.9651 21.2305 17.9199 21.5852 17.6722 21.8955C17.3872 22.2525 16.9551 22.4602 16.4983 22.4597C16.1013 22.4593 15.7961 22.273 15.6386 22.1689C15.474 22.06 15.2868 21.9102 15.1031 21.7632L12.69 19.8327C12.1714 19.4178 12.0174 19.3007 11.8575 19.219C11.697 19.137 11.5262 19.0771 11.3496 19.0408C11.1737 19.0047 10.9803 19 10.3162 19H7.75858C6.95362 19 6.28927 19 5.74808 18.9558C5.18598 18.9099 4.66928 18.8113 4.18394 18.564C3.43129 18.1805 2.81937 17.5686 2.43588 16.816C2.18859 16.3306 2.09002 15.8139 2.0441 15.2518C1.99988 14.7106 1.99989 14.0463 1.9999 13.2413V7.75868C1.99989 6.95372 1.99988 6.28936 2.0441 5.74818C2.09002 5.18608 2.18859 4.66937 2.43588 4.18404C2.81937 3.43139 3.43129 2.81947 4.18394 2.43598C4.66928 2.18868 5.18598 2.09012 5.74808 2.04419C6.28927 1.99998 6.95364 1.99999 7.7586 2ZM10.5073 7.5C10.5073 6.67157 9.83575 6 9.00732 6C8.1789 6 7.50732 6.67157 7.50732 7.5C7.50732 8.32843 8.1789 9 9.00732 9C9.83575 9 10.5073 8.32843 10.5073 7.5ZM16.6073 11.7001C16.1669 11.3697 15.5426 11.4577 15.2105 11.8959C15.1488 11.9746 15.081 12.0486 15.0119 12.1207C14.8646 12.2744 14.6432 12.4829 14.3566 12.6913C13.7796 13.111 12.9818 13.5001 12.0073 13.5001C11.0328 13.5001 10.235 13.111 9.65799 12.6913C9.37138 12.4829 9.15004 12.2744 9.00274 12.1207C8.93366 12.0486 8.86581 11.9745 8.80418 11.8959C8.472 11.4577 7.84775 11.3697 7.40732 11.7001C6.96549 12.0314 6.87595 12.6582 7.20732 13.1001C7.20479 13.0968 7.21072 13.1043 7.22094 13.1171C7.24532 13.1478 7.29407 13.2091 7.31068 13.2289C7.36932 13.2987 7.45232 13.3934 7.55877 13.5045C7.77084 13.7258 8.08075 14.0172 8.48165 14.3088C9.27958 14.8891 10.4818 15.5001 12.0073 15.5001C13.5328 15.5001 14.735 14.8891 15.533 14.3088C15.9339 14.0172 16.2438 13.7258 16.4559 13.5045C16.5623 13.3934 16.6453 13.2987 16.704 13.2289C16.7333 13.1939 16.7567 13.165 16.7739 13.1432C17.1193 12.6969 17.0729 12.0493 16.6073 11.7001ZM15.0073 6C15.8358 6 16.5073 6.67157 16.5073 7.5C16.5073 8.32843 15.8358 9 15.0073 9C14.1789 9 13.5073 8.32843 13.5073 7.5C13.5073 6.67157 14.1789 6 15.0073 6Z" fill="white"/>\n        </svg>',close:'<svg id="closeIcon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n          <path d="M18 18L6 6M6 18L18 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>\n        </svg>'},bubbleWindowWidth:380,bubbleWindowHeight:600},e),this.init()}return e.prototype.init=function(){this.createBubbleButton(),this.attachButtonEvents()},e.prototype.createBubbleButton=function(){"undefined"!=typeof document&&(document.getElementById(this.options.BUBBLE_BUTTON_ID)?this.bubbleButton=document.getElementById(this.options.BUBBLE_BUTTON_ID):(this.bubbleButton=document.createElement("div"),this.bubbleButton.id=this.options.BUBBLE_BUTTON_ID,this.bubbleButton.innerHTML=this.options.ICONS.open,this.bubbleButton.style.cssText="\n        position: fixed;\n        bottom: 20px;\n        right: 20px;\n        width: 60px;\n        height: 60px;\n        border-radius: 50%;\n        background-color: #5e5eff;\n        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        cursor: pointer;\n        z-index: 9999;\n        transition: all 0.3s ease;\n      ",document.body.appendChild(this.bubbleButton)))},e.prototype.createBubbleWindow=function(){if("undefined"!=typeof document&&!this.bubbleWindow){this.bubbleWindow=document.createElement("div"),this.bubbleWindow.id=this.options.BUBBLE_WINDOW_ID;var e="number"==typeof this.options.bubbleWindowWidth?"".concat(this.options.bubbleWindowWidth,"px"):this.options.bubbleWindowWidth||"380px",t="number"==typeof this.options.bubbleWindowHeight?"".concat(this.options.bubbleWindowHeight,"px"):this.options.bubbleWindowHeight||"600px";this.bubbleWindow.style.cssText="\n      position: fixed;\n      bottom: 90px;\n      right: 20px;\n      width: ".concat(e,";\n      height: ").concat(t,";\n      background-color: #fff;\n      border-radius: 16px;\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);\n      overflow: hidden;\n      z-index: 9998;\n      transition: all 0.3s ease;\n      display: none;\n    "),this.options.draggable&&(this.bubbleWindow.style.cursor="move",this.attachDragEvents()),document.body.appendChild(this.bubbleWindow)}},e.prototype.createIframe=function(){if(this.bubbleWindow&&!this.iframe){var e="dify-chatbot-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9));this.iframe=document.createElement("iframe"),this.iframe.style.cssText="\n      width: 100%;\n      height: 100%;\n      border: none;\n    ";var t=new URL("".concat(this.options.baseUrl,"?appKey=").concat(this.options.appKey));t.searchParams.append("embed","true"),t.searchParams.append("iframe_id",e),this.iframe.src=t.toString(),this.bubbleWindow.appendChild(this.iframe),this.initIframeCommunication()}},e.prototype.initIframeCommunication=function(){this.iframe&&(this.communicationClient=new g(this.iframe))},e.prototype.attachButtonEvents=function(){var e=this;this.bubbleButton&&this.bubbleButton.addEventListener("click",(function(){e.toggleChatWindow()}))},e.prototype.toggleChatWindow=function(){this.isOpen=!this.isOpen,this.isOpen?(this.bubbleWindow||this.createBubbleWindow(),this.iframe||this.createIframe(),this.bubbleWindow&&(this.bubbleWindow.style.display="block"),this.bubbleButton&&(this.bubbleButton.innerHTML=this.options.ICONS.close)):(this.bubbleWindow&&(this.bubbleWindow.style.display="none"),this.bubbleButton&&(this.bubbleButton.innerHTML=this.options.ICONS.open))},e.prototype.attachDragEvents=function(){var e=this;this.bubbleWindow&&this.options.draggable&&this.bubbleWindow.addEventListener("mousedown",(function(t){e.isDragging=!0,e.dragOffset.x=t.clientX-e.bubbleWindow.getBoundingClientRect().left,e.dragOffset.y=t.clientY-e.bubbleWindow.getBoundingClientRect().top;var n=function(t){if(e.isDragging){var n=t.clientX-e.dragOffset.x,o=t.clientY-e.dragOffset.y,i=window.innerWidth-e.bubbleWindow.offsetWidth,s=window.innerHeight-e.bubbleWindow.offsetHeight,r=Math.max(0,Math.min(n,i)),a=Math.max(0,Math.min(o,s));e.bubbleWindow.style.left="".concat(r,"px"),e.bubbleWindow.style.right="auto",e.bubbleWindow.style.top="".concat(a,"px"),e.bubbleWindow.style.bottom="auto"}},o=function(){e.isDragging=!1,document.removeEventListener("mousemove",n),document.removeEventListener("mouseup",o)};document.addEventListener("mousemove",n),document.addEventListener("mouseup",o)}))},e.prototype.sendMessage=function(e){var t=e.query,n=e.files,o=e.inputs;this.communicationClient?this.communicationClient.sendMessage({query:t,files:n,inputs:o}):console.warn("Chatbot is not initialized yet")},e.prototype.clearConversation=function(){this.communicationClient?this.communicationClient.clearConversation():console.warn("Chatbot is not initialized yet")},e.prototype.setInputs=function(e){this.communicationClient?this.communicationClient.setInputs(e):console.warn("Chatbot is not initialized yet")},e.prototype.getConversationHistory=function(){this.communicationClient?this.communicationClient.getConversationHistory():console.warn("Chatbot is not initialized yet")},e.prototype.setLanguage=function(e){this.communicationClient?this.communicationClient.setLanguage(e):console.warn("Chatbot is not initialized yet")},e.prototype.on=function(e,t){this.communicationClient?this.communicationClient.on(e,t):console.warn("Chatbot is not initialized yet")},e.prototype.open=function(){this.isOpen||this.toggleChatWindow()},e.prototype.close=function(){this.isOpen&&this.toggleChatWindow()},e.prototype.destroy=function(){this.bubbleButton&&this.bubbleButton.parentNode&&this.bubbleButton.parentNode.removeChild(this.bubbleButton),this.bubbleWindow&&this.bubbleWindow.parentNode&&this.bubbleWindow.parentNode.removeChild(this.bubbleWindow),this.iframe=null,this.bubbleButton=null,this.bubbleWindow=null,this.communicationClient=null},e}(),C="https://api.dify.ai/v1",w={application:{method:"GET",url:function(){return"/parameters"}},feedback:{method:"POST",url:function(e){return"/messages/".concat(e,"/feedbacks")}},createCompletionMessage:{method:"POST",url:function(){return"/completion-messages"}},createChatMessage:{method:"POST",url:function(){return"/chat-messages"}},getConversationMessages:{method:"GET",url:function(){return"/messages"}},getConversations:{method:"GET",url:function(){return"/conversations"}},renameConversation:{method:"POST",url:function(e){return"/conversations/".concat(e,"/name")}},deleteConversation:{method:"DELETE",url:function(e){return"/conversations/".concat(e)}},fileUpload:{method:"POST",url:function(){return"/files/upload"}},runWorkflow:{method:"POST",url:function(){return"/workflows/run"}},audioToText:{method:"POST",url:function(){return"/audio-to-text"}}},_=function(){function e(e,t){void 0===t&&(t=C),this.apiKey=e,this.baseUrl=t}return e.prototype.updateApiKey=function(e){this.apiKey=e},e.prototype.sendRequest=function(e,n,o,a,u,c){return void 0===o&&(o=null),void 0===a&&(a=null),void 0===u&&(u=!1),void 0===c&&(c={}),s(this,void 0,void 0,(function(){var s,d,l;return r(this,(function(r){switch(r.label){case 0:return s=i({Authorization:"Bearer ".concat(this.apiKey),"Content-Type":"application/json"},c),d="".concat(this.baseUrl).concat(n),u?[4,t({method:e,url:d,data:o,params:a,headers:s,responseType:"stream"})]:[3,2];case 1:return l=r.sent(),[3,4];case 2:return[4,t(i(i({method:e,url:d},"GET"!==e&&{data:o}),{params:a,headers:s,responseType:"json"}))];case 3:l=r.sent(),r.label=4;case 4:return[2,l]}}))}))},e.prototype.messageFeedback=function(e,t,n){var o={rating:t,user:n};return this.sendRequest(w.feedback.method,w.feedback.url(e),o)},e.prototype.getApplicationParameters=function(e){var t={user:e};return this.sendRequest(w.application.method,w.application.url(),null,t)},e.prototype.fileUpload=function(e){return this.sendRequest(w.fileUpload.method,w.fileUpload.url(),e,null,!1,{"Content-Type":"multipart/form-data"})},e.prototype.audioToText=function(e){return this.sendRequest(w.audioToText.method,w.audioToText.url(),e,null,!1,{"Content-Type":"multipart/form-data"})},e}(),T=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.createCompletionMessage=function(e,t,n,o){void 0===n&&(n=!1),void 0===o&&(o=null);var i={inputs:e,user:t,response_mode:n?"streaming":"blocking",files:o};return this.sendRequest(w.createCompletionMessage.method,w.createCompletionMessage.url(),i,null,n)},t.prototype.runWorkflow=function(e,t,n,o){void 0===n&&(n=!1),void 0===o&&(o=null);var i={inputs:e,user:t,response_mode:n?"streaming":"blocking",files:o};return this.sendRequest(w.runWorkflow.method,w.runWorkflow.url(),i,null,n)},t}(_),S=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.createChatMessage=function(e,t,n,o,i,s){void 0===o&&(o=!1),void 0===i&&(i=null),void 0===s&&(s=null);var r={inputs:e,query:t,user:n,response_mode:o?"streaming":"blocking",files:s};return i&&(r.conversation_id=i),this.sendRequest(w.createChatMessage.method,w.createChatMessage.url(),r,null,o)},t.prototype.getConversationMessages=function(e,t,n,o){void 0===t&&(t=""),void 0===n&&(n=null),void 0===o&&(o=null);var i={user:e};return t&&(i.conversation_id=t),n&&(i.first_id=n),o&&(i.limit=o),this.sendRequest(w.getConversationMessages.method,w.getConversationMessages.url(),null,i)},t.prototype.getConversations=function(e,t,n,o){void 0===t&&(t=null),void 0===n&&(n=null),void 0===o&&(o=null);var i={user:e};return t&&(i.first_id=t),n&&(i.limit=n),null!==o&&(i.pinned=o),this.sendRequest(w.getConversations.method,w.getConversations.url(),null,i)},t.prototype.renameConversation=function(e,t,n,o){var i={name:t,user:n,auto_generate:o};return this.sendRequest(w.renameConversation.method,w.renameConversation.url(e),i)},t.prototype.deleteConversation=function(e,t){var n={user:t};return this.sendRequest(w.deleteConversation.method,w.deleteConversation.url(e),n)},t}(_),I={application:{method:"GET",url:function(){return"/parameters"}},feedback:{method:"POST",url:function(e){return"/messages/".concat(e,"/feedbacks")}},createCompletionMessage:{method:"POST",url:function(){return"/completion-messages"}},createChatMessage:{method:"POST",url:function(){return"/chat-messages"}},stopChatMessage:{method:"POST",url:function(e){return"/chat-messages/".concat(e,"/stop")}},getConversationMessages:{method:"GET",url:function(){return"/messages"}},getConversations:{method:"GET",url:function(){return"/conversations"}},renameConversation:{method:"POST",url:function(e){return"/conversations/".concat(e,"/name")}},deleteConversation:{method:"DELETE",url:function(e){return"/conversations/".concat(e)}},fileUpload:{method:"POST",url:function(){return"/file-upload"}},runWorkflow:{method:"POST",url:function(){return"/workflows/run"}},audioToText:{method:"POST",url:function(){return"/audio-to-text"}},datasets:{method:"GET",url:function(){return"/datasets"}},createDocumentByFile:{method:"POST",url:function(e){return"/datasets/".concat(e,"/document/create-by-file")}},getDocumentIndexingStatus:{method:"GET",url:function(e,t){return"/datasets/".concat(e,"/documents/").concat(t,"/indexing-status")}},getDatasetDocuments:{method:"GET",url:function(e){return"/datasets/".concat(e,"/documents")}},getDatasetDocument:{method:"GET",url:function(e,t){return"/datasets/".concat(e,"/documents/").concat(t,"/upload-file")}},deleteDatasetDocument:{method:"DELETE",url:function(e,t){return"/datasets/".concat(e,"/documents/").concat(t)}}},W=null,B=null,x=null,E=function(){function e(e,t){void 0===e&&(e=a),void 0===t&&(t="app1"),this._enableWebSearch=!1,this.inputs={},this.baseUrl=e,this.appKey=t,this._enableWebSearch=!1,this.sessionId=this.getSessionFromStorage()}return Object.defineProperty(e.prototype,"enableWebSearch",{get:function(){return this._enableWebSearch},set:function(e){this._enableWebSearch=e},enumerable:!1,configurable:!0}),e.getInstance=function(t,n){return void 0===t&&(t=a),void 0===n&&(n="app1"),W||(W=new e(t,n)),W},e.prototype.setAppKey=function(e){this.appKey=e},e.prototype.setInputs=function(e){this.inputs=i({},e)},e.prototype.setSessionId=function(e){this.sessionId=e,this.saveSessionToStorage(e)},e.prototype.addInputs=function(e){this.inputs=i(i({},this.inputs),e)},e.prototype.sendRequest=function(e,t,n,o,a,u){return void 0===n&&(n=null),void 0===o&&(o=null),s(this,void 0,void 0,(function(){var s,u;return r(this,(function(r){switch(r.label){case 0:return s={},a&&(s.headers=i({},a)),o=i({},o=o||{}),this.appKey&&(o.app_key=this.appKey),this.sessionId&&(o.session_id=this.sessionId),n&&"object"==typeof n&&(n=i({},n),this.appKey&&(n.app_key=this.appKey),this.sessionId&&(n.session_id=this.sessionId)),"GET"!==e?[3,2]:(s.params=o,[4,f(t,s,{apiBaseUrl:this.baseUrl})]);case 1:return u=r.sent(),[3,8];case 2:return"POST"!==e?[3,4]:(s.params=o,s.body=n,[4,m(t,s,{apiBaseUrl:this.baseUrl})]);case 3:return u=r.sent(),[3,8];case 4:return"DELETE"!==e?[3,6]:(s.body=n,s.params=o,[4,v(t,s,{apiBaseUrl:this.baseUrl})]);case 5:return u=r.sent(),[3,8];case 6:return s.method=e,o&&(s.params=o),n&&(s.body=n),[4,f(t,s,{apiBaseUrl:this.baseUrl})];case 7:u=r.sent(),r.label=8;case 8:return this.handleSessionFromResponse(u),[2,u]}}))}))},e.prototype.getApplicationParameters=function(){return this.sendRequest(I.application.method,I.application.url())},e.prototype.messageFeedback=function(e,t,n){var o={rating:t};return n&&(o.user_id=n),this.sendRequest(I.feedback.method,I.feedback.url(e),o)},e.prototype.fileUpload=function(e){return s(this,void 0,void 0,(function(){var t,n;return r(this,(function(o){return(t=new FormData).append("file",e),n="".concat(I.fileUpload.url(),"?app_key=").concat(this.appKey),console.log(n),[2,b(n,t,{apiBaseUrl:this.baseUrl})]}))}))},e.prototype.audioToText=function(e,t){return void 0===t&&(t="audio/mp3"),s(this,void 0,void 0,(function(){var n;return r(this,(function(o){return(n=new FormData).append("file",e),n.append("type",t),this.appKey&&n.append("app_key",this.appKey),[2,b("".concat(I.audioToText.url(),"?app_key=").concat(this.appKey),n,{apiBaseUrl:this.baseUrl})]}))}))},e.prototype.getDatasets=function(){return s(this,void 0,void 0,(function(){return r(this,(function(e){return[2,this.sendRequest(I.datasets.method,I.datasets.url())]}))}))},e.prototype.createDocumentByFile=function(e,t){return s(this,void 0,void 0,(function(){return r(this,(function(n){return[2,this.sendRequest(I.createDocumentByFile.method,I.createDocumentByFile.url(e),t)]}))}))},e.prototype.getDocumentIndexingStatus=function(e,t){return s(this,void 0,void 0,(function(){return r(this,(function(n){return[2,this.sendRequest(I.getDocumentIndexingStatus.method,I.getDocumentIndexingStatus.url(e,t))]}))}))},e.prototype.getDatasetDocuments=function(e,t,n,o){return void 0===t&&(t=""),void 0===n&&(n=1),void 0===o&&(o=20),s(this,void 0,void 0,(function(){return r(this,(function(i){return[2,this.sendRequest(I.getDatasetDocuments.method,I.getDatasetDocuments.url(e),void 0,{keyword:t,page:n,limit:o})]}))}))},e.prototype.deleteDatasetDocument=function(e,t){return s(this,void 0,void 0,(function(){return r(this,(function(n){return[2,this.sendRequest(I.deleteDatasetDocument.method,I.deleteDatasetDocument.url(e,t))]}))}))},e.prototype.getDatasetDocument=function(e,t){return s(this,void 0,void 0,(function(){return r(this,(function(n){return[2,this.sendRequest(I.getDatasetDocument.method,I.getDatasetDocument.url(e,t))]}))}))},e.prototype.getSessionFromStorage=function(){if("undefined"!=typeof window&&window.localStorage)try{return localStorage.getItem("dify_session_id")||void 0}catch(e){return}},e.prototype.saveSessionToStorage=function(e){if("undefined"!=typeof window&&window.localStorage)try{localStorage.setItem("dify_session_id",e),localStorage.setItem("dify_session_".concat(this.appKey),e)}catch(e){}},e.prototype.handleSessionFromResponse=function(e){e&&e.session_id&&(this.sessionId=e.session_id,this.saveSessionToStorage(e.session_id))},e}(),k=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.getInstance=function(e,n){return void 0===e&&(e=a),void 0===n&&(n="app1"),B||(B=new t(e,n)),B},t.prototype.createCompletionMessage=function(e,t,n){return s(this,void 0,void 0,(function(){var o;return r(this,(function(s){return o={inputs:i(i({},this.inputs),e)},t&&(o.user=t),n&&n.length>0&&(o.files=n),[2,this.sendRequest(I.createCompletionMessage.method,I.createCompletionMessage.url(),o)]}))}))},t.prototype.runWorkflow=function(e,t){return s(this,void 0,void 0,(function(){var n;return r(this,(function(o){return n={inputs:i(i({},this.inputs),e)},t&&t.length>0&&(n.files=t),[2,this.sendRequest(I.runWorkflow.method,I.runWorkflow.url(),n)]}))}))},t.prototype.streamCompletionMessage=function(e,t,n,o){var s={inputs:i(i({},this.inputs),e),response_mode:"streaming"};t&&(s.user=t),n&&n.length>0&&(s.files=n),this.appKey&&(s.app_key=this.appKey),this.sessionId&&(s.session_id=this.sessionId);var r=i({apiBaseUrl:this.baseUrl},o);h(I.createCompletionMessage.url(),{body:s},r)},t}(E),D=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.message_files=[],t}return o(t,e),t.getInstance=function(e,n){return void 0===e&&(e=a),void 0===n&&(n="app1"),x||(x=new t(e,n)),x},t.prototype.createChatMessage=function(e,t,n,o,a,u){return void 0===n&&(n="streaming"),s(this,void 0,void 0,(function(){var s;return r(this,(function(r){return this.enableWebSearch&&(e||(e={}),e.web_search=!0),s={inputs:i(i({},this.inputs),e),query:t,response_mode:n},o&&(s.conversation_id=o),a&&(s.user=a),this.sessionId&&(s.session_id=this.sessionId),u&&u.length>0&&(s.files=u),[2,fetch("".concat(this.baseUrl).concat(I.createChatMessage.url(),"?app_key=").concat(this.appKey),{method:I.createChatMessage.method,headers:{"Content-Type":"application/json"},body:JSON.stringify(s),credentials:"include"})]}))}))},t.prototype.streamChatMessage=function(e,t){var n=e.inputs,o=e.query,s=e.conversation_id,r=e.user,a=e.files;this.enableWebSearch&&(n||(n={}),n.web_search=!0);var u={inputs:i(i({},this.inputs),n),query:o,response_mode:"streaming"};if(s&&(u.conversation_id=s),r&&(u.user=r),a&&a.length>0&&(u.files=a),this.message_files&&this.message_files.length>0){var c=this.message_files.map((function(e){return{type:e.type,transfer_method:e.transfer_method,url:"",upload_file_id:e.id}}));console.log(c)}this.appKey&&(u.app_key=this.appKey),this.sessionId&&(u.session_id=this.sessionId);var d=i({apiBaseUrl:this.baseUrl},t);h(I.createChatMessage.url(),{body:u},d)},t.prototype.getConversationMessages=function(e,t,n){return s(this,void 0,void 0,(function(){var o,i,s=this;return r(this,(function(r){switch(r.label){case 0:return o={conversation_id:e},this.message_files=[],t&&(o.first_id=t),n&&(o.limit=n),[4,this.sendRequest(I.getConversationMessages.method,I.getConversationMessages.url(),null,o)];case 1:return i=r.sent(),console.log(" result --- ",i),i.data.map((function(e){var t;e.message_files&&e.message_files.length>0&&(t=s.message_files).push.apply(t,e.message_files)})),console.log(" conversation msg --- ",i),[2,i]}}))}))},t.prototype.getConversations=function(e,t,n){var o={};return e&&(o.first_id=e),t&&(o.limit=t),void 0!==n&&(o.pinned=n),this.sendRequest(I.getConversations.method,I.getConversations.url(),null,o)},t.prototype.renameConversation=function(e,t,n){return void 0===n&&(n=!1),this.sendRequest(I.renameConversation.method,I.renameConversation.url(e),{name:t,auto_generate:n})},t.prototype.deleteConversation=function(e){return this.sendRequest(I.deleteConversation.method,I.deleteConversation.url(e))},t.prototype.stopChatMessage=function(e,t){var n={};return t&&(n.user=t),this.sendRequest(I.stopChatMessage.method,I.stopChatMessage.url(e),n)},t}(E);e.API_PREFIX=a,e.BASE_URL=C,e.ChatClientServer=S,e.ChatClientWeb=D,e.CompletionClientServer=T,e.CompletionClientWeb=k,e.DifyChatbot=y,e.DifyChatboxComunication=g,e.DifyClientServer=_,e.DifyClientWeb=E,e.audioToText=function(e,t,n){return void 0===t&&(t="audio/mp3"),s(void 0,void 0,void 0,(function(){var o;return r(this,(function(i){return(o=new FormData).append("file",e),o.append("type",t),[2,b("audio-to-text",o,{apiBaseUrl:n})]}))}))},e.del=v,e.deleteConversation=function(e,t){return s(void 0,void 0,void 0,(function(){return r(this,(function(n){return[2,v("conversations/".concat(e),{},{apiBaseUrl:t})]}))}))},e.fetchAppParams=function(e){return s(void 0,void 0,void 0,(function(){return r(this,(function(t){return[2,f("parameters",{},{apiBaseUrl:e})]}))}))},e.fetchChatList=function(e,t){return s(void 0,void 0,void 0,(function(){return r(this,(function(n){return[2,f("messages",{params:{conversation_id:e,limit:20,last_id:""}},{apiBaseUrl:t})]}))}))},e.fetchConversations=function(e){return s(void 0,void 0,void 0,(function(){return r(this,(function(t){return[2,f("conversations",{params:{limit:100,first_id:""}},{apiBaseUrl:e})]}))}))},e.generationConversationName=function(e,t,n){return void 0===t&&(t=""),s(void 0,void 0,void 0,(function(){return r(this,(function(o){return[2,m("conversations/".concat(e,"/name"),{body:{auto_generate:!0,name:t}},{apiBaseUrl:n})]}))}))},e.get=f,e.handleStreamingResponse=function(e,t){var n,o=t.onData,i=t.onCompleted,s=t.onThought,r=t.onFile,a=t.onError,u=t.onMessageEnd,c=t.onMessageReplace,d=t.onWorkflowStarted,l=t.onNodeStarted,h=t.onNodeFinished,p=t.onWorkflowFinished;if(e.ok){var f,m=null===(n=e.body)||void 0===n?void 0:n.getReader(),v=new TextDecoder("utf-8"),b="",g=!0;!function e(){var t=!1;null==m||m.read().then((function(n){if(n.done)i&&i();else{var a=(b+=v.decode(n.value,{stream:!0})).split("\n");try{a.forEach((function(e){if(e.startsWith("data: ")){try{f=JSON.parse(e.substring(6))}catch(e){return void(null==o||o("",g,{conversationId:null==f?void 0:f.conversation_id,messageId:null==f?void 0:f.message_id}))}if(400===f.status||!f.event)return null==o||o("",!1,{conversationId:void 0,messageId:"",errorMessage:null==f?void 0:f.message,errorCode:null==f?void 0:f.code}),t=!0,void(null==i||i(!0));"message"===f.event||"agent_message"===f.event?(null==o||o(f.answer,g,{conversationId:f.conversation_id,taskId:f.task_id,messageId:f.id}),g=!1):"agent_thought"===f.event?null==s||s(f):"message_file"===f.event?null==r||r(f):"message_end"===f.event?null==u||u(f):"message_replace"===f.event?null==c||c(f):"workflow_started"===f.event?null==d||d(f):"workflow_finished"===f.event?null==p||p(f):"node_started"===f.event?null==l||l(f):"node_finished"===f.event&&(null==h||h(f))}})),b=a[a.length-1]}catch(e){return null==o||o("",!1,{conversationId:void 0,messageId:"",errorMessage:"".concat(e)}),t=!0,void(null==i||i(!0))}t||e()}})).catch((function(e){null==a||a("".concat(e)),null==i||i(!0)}))}()}else null==a||a("Stream request failed with status ".concat(e.status))},e.post=m,e.postFormData=b,e.put=function(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={}),p(e,Object.assign({},t,{method:"PUT"}),n)},e.request=p,e.routes=w,e.sendChatMessage=function(e,t,n){var o=t.onData,a=t.onCompleted,u=t.onThought,c=t.onFile,d=t.onError,l=t.getAbortController,p=t.onMessageEnd,f=t.onMessageReplace,m=t.onWorkflowStarted,v=t.onNodeStarted,b=t.onNodeFinished,g=t.onWorkflowFinished;return s(void 0,void 0,void 0,(function(){return r(this,(function(t){return[2,h("chat-messages",{body:i(i({},e),{response_mode:"streaming"})},{onData:o,onCompleted:a,onThought:u,onFile:c,onError:d,getAbortController:l,onMessageEnd:p,onMessageReplace:f,onNodeStarted:v,onWorkflowStarted:m,onWorkflowFinished:g,onNodeFinished:b,apiBaseUrl:n})]}))}))},e.ssePost=h,e.updateFeedback=function(e,t){var n=e.url,o=e.body;return s(void 0,void 0,void 0,(function(){return r(this,(function(e){return[2,m(n,{body:o},{apiBaseUrl:t})]}))}))},e.upload=function(e,t){var n=t.apiBaseUrl,o="".concat(n||a,"/file-upload"),s={method:"POST",url:"".concat(o),data:{}},r=i(i({},s),e);return new Promise((function(e,t){var n=r.xhr;for(var o in n.open(r.method,r.url),r.headers)n.setRequestHeader(o,r.headers[o]);n.withCredentials=!0,n.onreadystatechange=function(){4===n.readyState&&(200===n.status?e({id:n.response}):t(n))},n.upload.onprogress=r.onprogress,n.send(r.data)}))},e.webRoutes=I}));
