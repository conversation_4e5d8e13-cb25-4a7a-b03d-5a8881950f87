<template>
  <div class="ai-chat-contaienr">
    <div class="absolute top-0 left-0 w-full h80 bg-transparent cursor-move drag-container"></div>
    <BubbleList :key="currentConversationId" :list="list">
      <!-- <template #header="{ item }">
        <Thinking
          v-if="item.thinkingStatus"
          :content="item.thinkingContent"
          :status="item.thinkingStatus"
          autoCollapse
          class="thinking-chain-warp"
          color="#333333"
          maxWidth="1700px"
          @change="handleThinkingChange" />
      </template> -->

      <!-- 自定义内容部分 -->
      <template #content="{ item }">
        <div v-if="item.role === 'user'" class="user-message">
          <div class="message-content">
            <div class="message-text">{{ item.content }}</div>
            <div v-if="item.timestamp" class="message-time">
              {{ new Date(item.timestamp).toLocaleTimeString() }}
            </div>
          </div>
        </div>
        <Answer v-else :key="item.key" :content="item.content" :timestamp="item.timestamp" :loading="item.loading" />
      </template>

      <!-- 自定义底部 -->
      <template #footer="{ item }">
        <div class="quote-list" v-if="item?.quoteList?.length > 0">
          <!-- <div class="quote-count-badge">{{ getUniqueQuotes(item.quoteList).length }}</div> -->
          <div class="quote-items">
            <el-popover
              v-for="(quoteGroup, index) in getUniqueQuotes(item.quoteList)"
              :key="index"
              placement="top"
              :width="400"
              trigger="hover"
              popper-class="quote-popover">
              <template #reference>
                <div class="quote-item" @click="handleQuote(quoteGroup)">
                  <div class="quote-item-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="quote-item-info">
                    <div class="quote-item-title">{{ quoteGroup.document_name || quoteGroup.fileName }}</div>
                  </div>
                </div>
              </template>
              <div class="quote-detail">
                <div class="quote-detail-header">
                  <div class="quote-detail-title">{{ quoteGroup.document_name || quoteGroup.fileName }}</div>
                </div>
                <div class="quote-detail-subtitle">{{ quoteGroup.dataset_name }}</div>
                <div class="quote-detail-content">
                  <div v-for="(quote, idx) in quoteGroup.quotes" :key="idx" class="quote-content-item">
                    <div class="quote-content-header">
                      <span class="quote-content-number">#{{ idx + 1 }}</span>
                    </div>
                    <div class="quote-content-text">{{ quote.content }}</div>
                  </div>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
        <div v-if="item.echartsJSONData" class="echarts-container">
          <VChart :key="item.key" :option="item.echartsJSONData" style="width: 800px; height: 600px" />
        </div>
      </template>
      <!-- <template #footer="{ item }">
        <div class="footer-container" v-if="item.role === 'ai'">
          <el-icon @click="handleOper('复制', item)"><DocumentCopy /></el-icon>
          <el-icon @click="handleOper('刷新', item)"><Refresh /></el-icon>
          <el-icon @click="handleOper('删除', item)"><Delete /></el-icon>
        </div>
      </template> -->
    </BubbleList>
    <!-- 欢迎页 -->
    <WelcomePage mt30 v-if="showWelcomePage && list.length === 0" @question="handleQuestion"></WelcomePage>
    <Sender
      ref="refSender"
      class="sender-container"
      :class="{ 'sender-with-files': uploadedFiles.length > 0 }"
      v-model="inputValue"
      @submit="handleQuestion(null)"
      variant="updown"
      allow-speech
      clearable
      :auto-size="{ minRows: 2, maxRows: 15 }"
      :input-style="{ color: '#000', fontSize: '16px' }"
      style="overflow: hidden"
      :loading="submitLoading">
      <template #header>
        <FileList @change="handleFileListChange" class="file-upload-area" style="margin-bottom: 16px" />
      </template>
      <template #prefix>
        <div class="tools-btns">
          <div
            class="px-8 py-5 border border-solid border-#333333 rounded-8 hover:bg-#f5f5f5 c-#333333 transition-colors duration-300 cursor-pointer"
            :class="{ selected: appName === currentAppKey }"
            v-for="(appName, index) in appNames"
            :key="index"
            @click="handleSelectAppNames(appName)">
            {{ appName }}
          </div>
        </div>
      </template>
      <template #action-list>
        <div style="display: flex; gap: 8px; align-items: center">
          <el-button class="sender-action-btn" round color="rgba(255,255,255,0.74)" @click="handleClear">
            <el-icon size="22px" color="#000000"><Delete /></el-icon>
          </el-button>

          <!-- 文件上传按钮 -->
          <el-popover placement="top" :width="200" trigger="hover" v-if="allowedFileExtensions">
            <template #reference>
              <el-button class="sender-action-btn" round color="rgba(255,255,255,0.74)">
                <el-icon size="22px" color="#000000"><Upload /></el-icon>
              </el-button>
            </template>
            <div class="upload-methods">
              <div v-if="fileUploadMethods.includes('local_file')" class="upload-method-item" @click="handleLocalFileUpload">
                <el-icon><Upload /></el-icon>
                <span>本地文件</span>
              </div>
              <div v-if="fileUploadMethods.includes('remote_url')" class="upload-method-item" @click="handleRemoteUrlUpload">
                <el-icon><Link /></el-icon>
                <span>远程链接</span>
              </div>
            </div>
          </el-popover>

          <!-- 语音设置按钮 -->
          <!-- <el-popover
            placement="top"
            :width="280"
            trigger="click"
          >
            <template #reference>
              <el-button class="sender-action-btn" round color="rgba(255,255,255,0.74)">
                <el-icon size="22px" color="#000000"><Setting /></el-icon>
              </el-button>
            </template>
            <div class="voice-settings">
              <div class="setting-item">
                <div class="setting-label">语音设置</div>
                <div class="setting-control">
                  <el-switch
                    v-model="autoExitWakeupAfterSend"
                    active-text="发送后自动退出唤醒"
                    inactive-text="保持唤醒状态"
                    style="--el-switch-on-color: #4FC3F7; --el-switch-off-color: #dcdfe6"
                  />
                </div>
              </div>
            </div>
          </el-popover> -->

          <!-- <VoiceButton
            ref="voiceButtonRef"
            :auto-exit-wakeup="autoExitWakeupAfterSend"
            @transcription="handleVoiceTranscription"
            @wakeup-changed="handleWakeupChanged"
            @connection-changed="handleConnectionChanged" /> -->
          <el-button
            class="sender-action-btn"
            round
            color="rgba(255,255,255,0.74)"
            :loading="submitLoading"
            v-if="!submitLoading"
            @click="handleQuestion(null)"
            :disabled="inputValue.length === 0">
            <el-icon size="22px" color="#000000"><Promotion /></el-icon>
          </el-button>
          <el-button class="sender-action-btn" v-else type="danger" circle @click="handleCancel">
            <el-icon class="is-loaidng">
              <Loading />
            </el-icon>
          </el-button>
        </div>
      </template>
    </Sender>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
  import { DocumentCopy, Refresh, Delete, Document, CloseBold, Promotion, Microphone, Loading, Setting, Upload, Link } from '@element-plus/icons-vue'
  import { BubbleList, Thinking, Sender } from 'vue-element-plus-x'
  import { ElMessage, ElPopover } from 'element-plus'
  import WelcomePage from './components/WelcomePage/index.vue' // 引入WelcomePage组件 欢迎页
  import VoiceButton from './components/VoiceButton.vue' // 引入语音按钮组件
  import Answer from './components/Answer/index.vue' // 引入Answer组件
  import FileList from './components/FileList/index.vue' // 引入FileList组件
  import VChart from 'vue-echarts' // 引入 vue-echarts 组件
  import { use } from 'echarts/core'
  import { CanvasRenderer } from 'echarts/renderers'
  import { PieChart, BarChart, LineChart } from 'echarts/charts'
  import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
  import { useRoute } from 'vue-router'

  // 注册必要的组件
  use([CanvasRenderer, PieChart, BarChart, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

  import { useAiChat, useAppState, getSupportedExtensions, getFileType, getFileSizeLimit } from './helper'
  const emits = defineEmits(['close'])

  const refSender = ref(null)
  const voiceButtonRef = ref(null)

  // 使用新的应用状态管理
  const { appKeyMap, appNames, currentAppKey, globalAppParameters, setCurrentAppKey, getCurrentAppKeyValue } = useAppState()
  console.log('当前选中的工具:', appNames.value, currentAppKey.value)
  console.log('当前应用参数:', globalAppParameters.value)

  // 监听应用参数变化
  watch(
    globalAppParameters,
    (newParams) => {
      console.log('应用参数更新:', newParams)
      if (newParams.file_upload) {
        console.log('文件上传配置:', newParams.file_upload)
      }
    },
    { deep: true }
  )

  // 计算允许的文件扩展名
  const allowedFileExtensions = computed(() => {
    if (!globalAppParameters.value.file_upload || !globalAppParameters.value.file_upload.enabled) {
      return ''
    }
    // 使用 allowed_file_types 获取支持的扩展名
    const allowedTypes = globalAppParameters.value.file_upload.allowed_file_types || []
    const extensions = getSupportedExtensions(allowedTypes)
    // 转换为 accept 属性格式
    return extensions.map((ext) => `.${ext.toLowerCase()}`).join(',')
  })

  // 允许的上传方式
  const fileUploadMethods = computed(() => {
    if (!globalAppParameters.value.file_upload || !globalAppParameters.value.file_upload.enabled) {
      return []
    }
    return globalAppParameters.value.file_upload.allowed_file_upload_methods || []
  })

  const showWelcomePage = ref(true) // 控制欢迎页的显示 默认显示
  const autoExitWakeupAfterSend = ref(true) // 控制是否在发送完一次后自动退出唤醒状态，默认开启

  const handleSelectAppNames = async (item) => {
    setCurrentAppKey(item) // 使用新的状态管理方法
    currentConversationId.value = ''
    if (item !== '业务库') {
      //非业务科，则需要携带最后的五条历史记录
      isCarryHistory.value = true
    }

    // 手动调用 handleAppKeyChange 确保对话列表更新
    await handleAppKeyChange(getCurrentAppKeyValue())
  }

  const {
    inputValue,
    recording,
    submitLoading,
    list,
    currentConversationId,
    isCarryHistory,
    uploadedFiles, // 新增：使用 helper 提供的文件列表
    handleRecord,
    handleCancel,
    handleSubmit,
    handleClear: originalHandleClear,
    handleThinkingChange,
    handleAppKeyChange,
    handleFileUpload,
    // 新增：文件管理方法
    addUploadFile,
    removeUploadFile,
    updateFileStatus,
    clearUploadFiles,
    validateFile,
    uploadFileToServer
  } = useAiChat()

  // 监听文件列表变化，控制 header 显示
  watch(
    uploadedFiles,
    (newFiles) => {
      if (refSender.value) {
        if (newFiles.length > 0) {
          // 有文件时显示 header
          refSender.value.openHeader()
        } else {
          // 无文件时隐藏 header
          refSender.value.closeHeader()
        }
      }
    },
    { deep: true }
  )

  // 自定义清空方法，包含文件清空
  const handleClear = () => {
    originalHandleClear()
    // clearUploadedFiles() 已经在 originalHandleClear 中处理了
  }

  const handleClose = () => {
    console.log('handleClose')
    emits('close')
  }

  const handleQuestion = async (question) => {
    showWelcomePage.value = false // 点击问题后，隐藏欢迎页
    question && (inputValue.value = question)

    // 处理上传的文件
    if (uploadedFiles.value.length > 0) {
      console.log('包含上传文件:', uploadedFiles.value)

      try {
        // 遍历每个文件并上传
        for (const file of uploadedFiles.value) {
          if (file.status === 'ready' && file.raw) {
            await uploadFileToServer(file)
          }
        }

        // 构建文件信息
        const fileInfo = uploadedFiles.value
          .map((file) => {
            if (file.status === 'success') {
              return `[已上传文件: ${file.name}]`
            } else {
              return `[文件: ${file.name}]`
            }
          })
          .join(' ')

        if (fileInfo) {
          if (inputValue.value) {
            inputValue.value = `${inputValue.value}\n\n${fileInfo}`
          } else {
            inputValue.value = fileInfo
          }
        }
      } catch (error) {
        console.error('文件处理失败:', error)
      }
    }

    handleSubmit()
  }

  onMounted(() => {
    console.log('初始化')
    // 初始化时检查文件列表状态
    if (uploadedFiles.value.length > 0 && refSender.value) {
      refSender.value.openHeader()
    }

    // 获取url中的query参数，如果有则自动填充inputValue并提交
    const { query } = useRoute()
    const queryQuestion = query.q
    if (queryQuestion) {
      inputValue.value = queryQuestion
      showWelcomePage.value = false
      setTimeout(() => {
        handleSubmit()
      }, 1000)
    }
  })

  // 获取去重后的引用列表
  const getUniqueQuotes = (quoteList: any[]) => {
    const quoteGroups: any[] = []
    const seen = new Set()

    for (const quote of quoteList) {
      const fileName = quote.document_name || quote.fileName
      const datasetName = quote.dataset_name

      if (!seen.has(fileName)) {
        // 找到所有相同文件名的引用
        const sameFileQuotes = quoteList.filter((q) => (q.document_name || q.fileName) === fileName)

        // 创建合并后的引用组
        const quoteGroup = {
          document_name: fileName,
          dataset_name: datasetName,
          quotes: sameFileQuotes
        }

        quoteGroups.push(quoteGroup)
        seen.add(fileName)
      }
    }
    return quoteGroups
  }

  const handleQuote = (quoteGroup) => {
    console.log('点击引用:', quoteGroup)

    // 如果有URL，则打开链接
    if (quoteGroup.quotes && quoteGroup.quotes[0] && quoteGroup.quotes[0].url) {
      window.open(quoteGroup.quotes[0].url, '_blank')
    } else {
      // 显示引用详情
      ElMessage({
        message: `引用来源: ${quoteGroup.document_name || quoteGroup.fileName}\n数据集: ${quoteGroup.dataset_name}\n引用数量: ${quoteGroup.quotes.length}`,
        type: 'info',
        duration: 3000,
        showClose: true
      })
    }

    // 可以在这里添加更多处理逻辑，比如搜索相关文档等
    // lib.emitter.emit('handle-global-search-input-change', { value: quoteGroup.document_name || quoteGroup.fileName })
  }

  // 语音识别结果处理
  const handleVoiceTranscription = (text: string, timestamp: number) => {
    console.log('语音识别结果:', text)
    // 将语音识别结果填入输入框
    inputValue.value = text
    showWelcomePage.value = false // 隐藏欢迎页
    // 自动提交
    if (!submitLoading.value) {
      handleSubmit()
    }
  }

  // 唤醒状态变化处理
  const handleWakeupChanged = (isWakeup: boolean) => {
    console.log('唤醒状态变化:', isWakeup)
    // 可以在这里处理唤醒状态变化的逻辑
  }

  // 连接状态变化处理
  const handleConnectionChanged = (isConnected: boolean) => {
    console.log('语音服务连接状态:', isConnected)
    // 可以在这里处理连接状态变化的逻辑
  }

  // 文件上传处理
  // 文件列表变化处理
  const handleFileListChange = (files) => {
    uploadedFiles.value = files
    console.log('文件列表变化:', files)
  }

  // 清空上传的文件
  const clearUploadedFiles = () => {
    uploadedFiles.value = []
  }

  // 处理本地文件上传
  const handleLocalFileUpload = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = allowedFileExtensions.value
    input.multiple = true

    input.onchange = async (event) => {
      const target = event.target as HTMLInputElement
      const files = Array.from(target.files || [])
      await processFiles(files)
    }

    input.click()
  }

  // 处理远程链接上传
  const handleRemoteUrlUpload = () => {
    const url = prompt('请输入文件链接:')
    if (url && url.trim()) {
      const fileName = url.split('/').pop() || 'remote_file'
      const fileItem = {
        uid: Date.now().toString(),
        name: fileName,
        size: 0,
        type: getFileType(fileName) as 'document' | 'image' | 'video' | 'audio' | 'unknown',
        url: url.trim(),
        isRemote: true,
        status: 'success' as const,
        progress: 100
      }

      addUploadFile(fileItem)
      ElMessage.success(`文件链接 ${fileName} 已添加`)
    }
  }

  // 处理文件
  const processFiles = async (files) => {
    for (const file of files) {
      if (!validateFile(file)) continue

      const fileItem = {
        uid: Date.now().toString() + Math.random().toString(36).substr(2),
        name: file.name,
        size: file.size,
        type: getFileType(file.name) as 'document' | 'image' | 'video' | 'audio' | 'unknown',
        raw: file,
        status: 'ready' as const,
        progress: 0
      }

      addUploadFile(fileItem)

      // 立即上传文件
      await uploadFileToServer(fileItem)
    }
  }
</script>
<style scoped lang="scss">
  .ai-chat-contaienr {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
  }
  .user-message {
    max-width: 100%;
    font-size: 16px;
    line-height: 1.5;
    word-wrap: break-word;
    border-radius: 18px;
    .message-content {
      .message-text {
        margin-bottom: 4px;
      }
      .message-time {
        font-size: 12px;
        color: #90a4ae;
        text-align: right;
        opacity: 0.8;
      }
    }
  }
  .sender-container {
    flex: 0 0 auto;
    transition: min-height 0.3s ease;
    &.sender-with-files {
      min-height: calc(100px + 80px);
    }
  }
  .echarts-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }
  .file-preview-area {
    padding: 12px;
    margin: 10px 0;
    background: rgb(255 255 255 / 90%);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }
  .file-preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }
  .file-preview-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .file-preview-item {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px;
    background: rgb(255 255 255 / 80%);
    border: 1px solid rgb(0 0 0 / 10%);
    border-radius: 8px;
  }
  .file-name {
    flex: 1;
    overflow: hidden;
    font-size: 14px;
    color: #333333;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .file-size {
    font-size: 12px;
    color: #666666;
  }
  .remote-icon {
    margin-left: 8px;
    color: #409eff;
  }
  .remove-btn {
    min-height: auto !important;
    padding: 4px !important;
  }
  .tools-btns {
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: center;
    > div {
      padding: 0 20px;
      background: rgb(255 255 255 / 15%);
      backdrop-filter: blur(6px);
      border-radius: 16px;
      box-shadow: 0 2px 8px 0 rgb(80 150 255 / 8%);
      transition: background 0.2s, box-shadow 0.2s;
      &:hover,
      &.selected {
        color: #1976d2;
        background: rgb(67 158 253 / 25%);
        box-shadow: 0 4px 16px 0 rgb(67 158 253 / 18%);
      }
    }
  }
  .sender-action-btn {
    width: 40px;
    height: 40px;
    padding: 0;
    color: #222222 !important;
    background: rgb(255 255 255 / 15%) !important;
    backdrop-filter: blur(6px);
    border-radius: 50% !important;
    transition: background 0.2s, box-shadow 0.2s;
    &:hover,
    &:focus,
    &.is-active {
      color: #1976d2 !important;
      background: rgb(67 158 253 / 25%) !important;
      box-shadow: 0 4px 16px 0 rgb(67 158 253 / 18%);
    }
  }

  /* 上传方法菜单样式 */
  .upload-methods {
    padding: 8px 0;
  }
  .upload-method-item {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;
    &:hover {
      background-color: rgb(67 158 253 / 10%);
    }
    .el-icon {
      font-size: 16px;
      color: #409eff;
    }
    span {
      font-size: 14px;
      color: #333333;
    }
  }

  /* 引用列表样式 */
  .quote-list {
    position: relative;
    padding: 3px;
    margin-top: 8px;
    background: rgb(248 250 252 / 95%);
    border: 1px solid rgb(67 158 253 / 8%);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(67 158 253 / 6%);
  }
  .quote-count-badge {
    position: absolute;
    top: -6px;
    left: 12px;
    z-index: 1;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    color: #1976d2;
    background: rgb(67 158 253 / 10%);
    border-radius: 8px;
  }
  .quote-items {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  .quote-item {
    display: flex;
    gap: 6px;
    align-items: center;
    min-width: 180px;
    max-width: 280px;
    padding: 6px 10px;
    cursor: pointer;
    background: rgb(255 255 255 / 90%);
    border: 1px solid rgb(67 158 253 / 6%);
    border-radius: 6px;
    transition: all 0.2s ease;
    &:hover {
      background: rgb(255 255 255 / 95%);
      border-color: rgb(67 158 253 / 15%);
      box-shadow: 0 1px 4px rgb(67 158 253 / 8%);
      transform: translateY(-1px);
    }
  }
  .quote-item-icon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    font-size: 12px;
    color: white;
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    border-radius: 3px;
  }
  .quote-item-info {
    flex: 1;
    min-width: 0;
  }
  .quote-item-title {
    overflow: hidden;
    font-size: 12px;
    font-weight: 500;
    color: #333333;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 引用详情弹窗样式 */
  .quote-detail {
    .quote-detail-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    .quote-detail-title {
      overflow: hidden;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .quote-detail-subtitle {
      margin-bottom: 12px;
      font-size: 13px;
      color: #666666;
    }
    .quote-detail-content {
      margin-bottom: 12px;
      .quote-content-item {
        padding-bottom: 12px;
        margin-bottom: 12px;
        border-bottom: 1px solid rgb(67 158 253 / 8%);
        &:last-child {
          padding-bottom: 0;
          margin-bottom: 0;
          border-bottom: none;
        }
      }
      .quote-content-header {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
      }
      .quote-content-number {
        padding: 2px 6px;
        font-size: 11px;
        font-weight: 600;
        color: #1976d2;
        background: rgb(67 158 253 / 10%);
        border-radius: 4px;
      }
      .quote-content-text {
        font-size: 13px;
        line-height: 1.6;
        color: #555555;
      }
    }
  }
</style>
<style lang="scss">
  /* 全局popover样式 */
  .quote-popover {
    .el-popover__title {
      padding-bottom: 12px;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      border-bottom: 1px solid rgb(67 158 253 / 10%);
    }
    .el-popover__title .el-icon {
      font-size: 18px;
      color: #1976d2;
    }
    .quote-detail {
      .quote-detail-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      .quote-detail-title {
        overflow: hidden;
        font-size: 16px;
        font-weight: 600;
        color: #333333;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .quote-detail-subtitle {
        margin-bottom: 12px;
        font-size: 13px;
        color: #666666;
      }
      .quote-detail-content {
        margin-bottom: 12px;
        .quote-content-item {
          padding-bottom: 12px;
          margin-bottom: 12px;
          border-bottom: 1px solid rgb(67 158 253 / 8%);
          &:last-child {
            padding-bottom: 0;
            margin-bottom: 0;
            border-bottom: none;
          }
        }
        .quote-content-header {
          display: flex;
          align-items: center;
          margin-bottom: 6px;
        }
        .quote-content-number {
          padding: 2px 6px;
          font-size: 11px;
          font-weight: 600;
          color: #1976d2;
          background: rgb(67 158 253 / 10%);
          border-radius: 4px;
        }
        .quote-content-text {
          font-size: 13px;
          line-height: 1.6;
          color: #555555;
        }
      }
    }
  }

  /* 全局样式 */
  .ai-chat-contaienr {
    .el-bubble-content {
      font-size: 18px !important;
      line-height: 22px !important;
      color: #242424 !important;
    }
    .el-bubble-end {
      --el-fill-color: #9fc6ff;
    }
    .sender-style {
      background: rgb(255 255 255 / 40%);
      border-radius: 20px;
      box-shadow: inset 0 0 22px 1px rgb(255 255 255 / 73%);
      .el-sender {
        border: none !important;
      }
    }
    .el-textarea__inner::placeholder {
      color: #333333;
    }
  }
  .thinking-chain-warp {
    margin-bottom: 20px !important;
    .content pre,
    .label {
      font-size: 18px;
    }
  }
  .el-bubble-list {
    flex: 1 1 0;
    max-height: none !important;
  }
  .el-sender .el-sender-content .el-sender-prefix {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  /* 语音设置样式 */
  .voice-settings {
    padding: 12px 0;
  }
  .setting-item {
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .setting-label {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
  }
</style>
