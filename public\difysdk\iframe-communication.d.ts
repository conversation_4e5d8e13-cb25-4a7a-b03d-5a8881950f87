/**
 * Utility functions for iframe communication between Dify Chatbox and parent applications
 */
import type { MessageTypeEnum } from './DifyChatbot';
export type MessagePayload = {
    source: 'dify-chatbox' | 'parent-app';
    type: MessageTypeEnum;
    data: any;
};
/**
 * Send a message to the parent window
 * @param type - The type of message
 * @param data - The data to send
 * @param targetOrigin - The target origin (use '*' for development, but specific origins for production)
 */
export declare const sendMessageToParent: (type: MessageTypeEnum, data: any, targetOrigin?: string) => void;
/**
 * Initialize listener for messages from parent
 * @param handlers - Object containing handler functions for different message types
 */
export declare const initMessageListener: (handlers: {
    error?: (data: any) => void;
    ready?: (data: any) => void;
    "conversation-completed"?: (data: any) => void;
    "response-stream"?: (data: any) => void;
    "user-message-sent"?: (data: any) => void;
    "conversation-history"?: (data: any) => void;
    "feedback-updated"?: (data: any) => void;
    "send-message"?: (data: any) => void;
    "get-conversation-history"?: (data: any) => void;
    "clear-conversation"?: (data: any) => void;
    "set-inputs"?: (data: any) => void;
    "get-status"?: (data: any) => void;
    "set-language"?: (data: any) => void;
}) => () => void;
/**
 * Notify the parent window that the chatbox is ready
 * @param features - List of supported features
 */
export declare const notifyReady: (features?: string[]) => void;
/**
 * Send conversation results to parent window
 * @param question - The user's question
 * @param answer - The AI's answer
 * @param messageId - The message ID
 * @param conversationId - The conversation ID
 * @param agentThoughts - Optional agent thoughts (for agent mode)
 */
export declare const sendConversationResults: (question: string, answer: string, messageId: string, conversationId: string, agentThoughts?: any[]) => void;
/**
 * Send streaming response data to parent
 * @param partial - The partial response
 * @param messageId - The message ID
 * @param conversationId - The conversation ID
 * @param isFirstChunk - Whether this is the first chunk of the response
 */
export declare const sendResponseStream: (partial: string, messageId: string, conversationId: string, isFirstChunk?: boolean) => void;
/**
 * Send user message event to parent
 * @param message - The user's message
 * @param files - Optional files attached to the message
 * @param inputs - Optional input variables
 */
export declare const sendUserMessageEvent: (message: string, files?: any[], inputs?: Record<string, any>) => void;
/**
 * Send conversation history to parent
 * @param history - Array of conversation items
 */
export declare const sendConversationHistory: (history: any[]) => void;
/**
 * Send error message to parent
 * @param message - Error message
 * @param code - Optional error code
 */
export declare const sendError: (message: string, code?: string) => void;
/**
 * Send feedback update to parent
 * @param messageId - The message ID that received feedback
 * @param rating - The feedback rating
 */
export declare const sendFeedbackUpdate: (messageId: string, rating: string) => void;
/**
 * Check if we're running in an iframe
 */
export declare const isInIframe: () => boolean;
/**
 * A higher-level function that integrates all the message handling in one place
 * @param callbacks - Object with callbacks for different events
 * @returns cleanup function
 */
export declare const setupIframeCommunication: (callbacks: {
    onSendMessage?: (message: string, files?: any[], inputs?: Record<string, any>) => void;
    onClearConversation?: () => void;
    onSetInputs?: (inputs: Record<string, any>) => void;
    onSetLanguage?: (language: any) => void;
    onGetStatus?: () => any;
}) => () => void;
/**
 * Helper function to create a postMessage handler in the parent window
 * @param targetIframe - The iframe element or iframe window object
 * @param handlers - Object with handler functions for different message types
 * @returns Function to send messages to the iframe
 */
export declare const createParentHandler: (targetIframe: HTMLIFrameElement | Window, handlers: {
    error?: (data: any) => void;
    ready?: (data: any) => void;
    "conversation-completed"?: (data: any) => void;
    "response-stream"?: (data: any) => void;
    "user-message-sent"?: (data: any) => void;
    "conversation-history"?: (data: any) => void;
    "feedback-updated"?: (data: any) => void;
    "send-message"?: (data: any) => void;
    "get-conversation-history"?: (data: any) => void;
    "clear-conversation"?: (data: any) => void;
    "set-inputs"?: (data: any) => void;
    "get-status"?: (data: any) => void;
    "set-language"?: (data: any) => void;
}) => (type: MessageTypeEnum, data: any) => void;
