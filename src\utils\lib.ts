/*
 * @Author: wa<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-30 10:17:18
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-25 09:35:12
 * @FilePath: \bigscreen-qj-web\src\utils\lib.ts
 * @Description:封装Hooks，Provide，Store等
 *
 */
import * as api from '@/api/index.js'
import useStore from '@/store/index.js'
import { bus } from '@/utils/eventBus.js'
import * as utils from '@/utils/index'
import { provideTools } from '@/utils/provideMap.js'
import * as enumMap from './enums'

export default {
  /**
   * @description: Pinia
   */
  store: () => useStore(),
  /**
   * @description: EventBus统一管理
   */
  /**
   * @description: EventBus统一管理
   */
  bus,
  /**
   * @description: provide,inject统一管理
   */
  provideTools,
  /**
   * @description: 接口
   */
  api,
  /**
   * @description: 公用方法
   */
  utils,
  /** 当前跟随车辆车牌号 */
  currentFollowCarPlate: null,
  enumsList: {
    point: {
      突发事件撒点: 'emergencyPoint',
      设备撒点: 'devicePoint',
      设施撒点: 'structurePoint'
    },
    widows: {
      Ai对话框: 'AiDialog',
      养护对话框: 'YangHuDialog'
    }
  },
  codeBimInfoMap: new Map(),
  enumMap
}
