<template>
  <div class="yang-hu-container" @click="handleClick" v-if="type === '养护'"></div>
  <div class="yang-hu-container zj" @click="handleClick" v-else></div>
</template>
<script setup lang="ts">
  import lib from '@/utils/lib'

  const props = withDefaults(defineProps<{ type: '养护' | '浙江区域' }>(), {
    type: '养护'
  })

  const handleClick = () => {
    lib.bus.busYangHu.emit(false)
  }
</script>

<style lang="scss" scoped>
  .yang-hu-container {
    width: 3500px;
    height: 1000px;
    background: url('@/assets/yh.png');
    background-size: 100% 100%;
    &.zj {
      background: url('@/assets/zj.png');
      background-size: 100% 100%;
    }
  }
</style>
