<template>
  <div class="ai-robot-container" id="ai-robot" v-drag="{ draggableClass: 'robot-mask' }" @mousedown="startDrag" @mousemove="onDrag" @mouseup="endDrag">
    <img src="@/assets/ScreenMiddle/AiRobot/robot.gif" class="w240 h240 absolute top-0 left-0" v-if="submitLoading" />
    <img src="@/assets/ScreenMiddle/AiRobot/robot.png" class="h220 absolute top-15 left-89 cursor-pointer" v-else />
    <div class="h220 absolute top-15 left-89 cursor-pointer bg-transparent z-1000 w114 robot-mask" @click="handleShowSender"></div>
    <!-- <div class="absolute -top-300 -left-100 w-300 h-300">
      <el-button type="primary" @click="handleTest1">测试1</el-button>
      <el-button type="warning" @click="handleTest2">测试2</el-button>
      <el-button type="warning" @click="handleTest3">测试3</el-button>
      <el-button type="warning" @click="handleTest4">测试4</el-button>
      <el-button type="warning" @click="handleTest5">暂停播放</el-button>
      <el-button type="warning" @click="handleOpenAiWindow">打开AI窗口</el-button>
    </div> -->
    <div class="result-container" v-if="reply.length > 0" :class="{ zhanKai: isZhanKai }">
      <img
        src="@/assets/ScreenMiddle/AiRobot/shouqi.svg"
        class="absolute bottom-8 right-8 cursor-pointer w44 h44 cursor-pointer"
        v-if="isZhanKai"
        @click="isZhanKai = false" />
      <img
        src="@/assets/ScreenMiddle/AiRobot/zhankai.svg"
        class="absolute bottom-8 right-8 cursor-pointer w44 h44 cursor-pointer"
        v-else
        @click="isZhanKai = true" />
      <div class="result-content" ref="resultContentRef" @scroll="handleScroll">
        <Typewriter ref="typerRef" :content="reply" :typing="{ interval: 260 }" :is-markdown="true" isFog />
      </div>
    </div>
    <div class="sender-container animate__animated animate__faster animate__flipInX" v-show="showSender && !fromQuestion">
      <Sender
        class="sender-style"
        ref="refSender"
        :input-style="{ color: '#000000', fontSize: '25px', backgroundColor: 'transparent', backDropFilter: 'blur(10px)' }"
        v-model="inputValue"
        :loading="submitLoading"
        @submit="handleSubmit">
        <template #action-list>
          <div style="display: flex; gap: 8px; align-items: center; width: 220px">
            <div class="tools-btns flex mt-6 ml-20">
              <!-- <div
                class="px-8 py-5 border border-solid border-#333333 rounded-8 hover:bg-#f5f5f5 c-#333333 transition-colors duration-300 cursor-pointer"
                :class="{ selected: !isQuestionType }"
                v-for="(item, index) in toolsBtns"
                :key="index"
                @click="handleSelectTools(item)">
                {{ !isQuestionType ? item : '提问' }}
              </div> -->
            </div>
            <el-button round color="rgba(255,255,255,0.74)" @click="handleRecord">
              <el-icon size="22px" color="#000000">
                <Microphone v-if="!recording" />
                <Loading v-else />
              </el-icon>
            </el-button>
            <el-button
              round
              color="rgba(255,255,255,0.74)"
              :loading="submitLoading"
              v-if="!submitLoading"
              @click="handleSubmit"
              :disabled="inputValue.length === 0">
              <el-icon size="22px" color="#000000"><Promotion /></el-icon>
            </el-button>
            <el-button v-else type="danger" circle @click="handleCancel">
              <el-icon class="is-loaidng">
                <Loading />
              </el-icon>
            </el-button>
          </div>
        </template>
      </Sender>
      <!-- <div class="tools-btns flex mt-6 ml-20">
        <div
          class="px-8 py-5 border border-solid border-#333333 rounded-8 hover:bg-#f5f5f5 c-#333333 transition-colors duration-300 cursor-pointer"
          :class="{ selected: !isQuestionType }"
          v-for="(item, index) in toolsBtns"
          :key="index"
          @click="handleSelectTools(item)">
          {{ !isQuestionType ? item : '提问' }}
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useAiRobot } from './helper/index'
  import CreateMqtt from '@/utils/mqtt.js'
  import { vDrag } from 'znyg-frontend-common'
  import { storeToRefs } from 'pinia'

  import lib from '@/utils/lib'
  import Scene1 from '@/assets/audio/首页欢迎词.mp3'
  import Scene2_1 from '@/assets/audio/收到，这就打开作业监管场景.mp3'
  import Scene2_2 from '@/assets/audio/好的，您可以通过呼叫“慧管家”随时打断我.mp3'
  import Scene2_3 from '@/assets/audio/介绍.mp3'
  import Scene3_1 from '@/assets/audio/好的，马上统计.mp3'
  import Scene3_2 from '@/assets/audio/今日总计作业124项.mp3'
  import wakeAudio from '@/assets/audio/我在.mp3'
  import okAudio from '@/assets/audio/好的.mp3'

  const audioList = {
    我在: new Audio(wakeAudio),
    好的: new Audio(okAudio),
    介绍: new Audio(Scene1),
    场景2_1: new Audio(Scene2_1),
    场景2_2: new Audio(Scene2_2),
    场景2_3: new Audio(Scene2_3),
    场景3_1: new Audio(Scene3_1),
    场景3_2: new Audio(Scene3_2)
  }

  audioList.场景2_3.addEventListener('ended', () => {
    isIntroduceing.value = false
  })

  const fromQuestion = ref(false)
  const toolsBtns = ['操作']
  const selectedTools = ref('操作')
  const isDragging = ref(false)
  const mouseDownPos = reactive({ x: 0, y: 0 })
  const dragThreshold = 5 // 拖动阈值，移动超过这个像素才算拖动
  const isZhanKai = ref(false)

  const isQuestionType = computed(() => {
    return lib.store().storeScreenData.isQuestionType
  })
  const { robotPosition } = storeToRefs(lib.store().storeScreenData)

  watch(isQuestionType, (newVal) => {
    selectedTools.value = newVal ? '操作' : ''
  })

  const handleSelectTools = (item) => {
    lib.store().storeScreenData.isQuestionType = !lib.store().storeScreenData.isQuestionType
    // if (selectedTools.value === item) {
    //   lib.store().storeScreenData.isQuestionType = true
    //   // selectedTools.value = ''
    // } else {
    //   lib.store().storeScreenData.isQuestionType = false
    //   // selectedTools.value = item
    // }
  }
  const appKey = computed(() => {
    return lib.store().storeScreenData.isQuestionType ? 'qjxiangmuwenda' : 'BimEngineCode2'
  })
  const { inputValue, recording, submitLoading, reply, showSender, currentConversationId, handleRecord, handleCancel, handleSubmit } = useAiRobot(appKey)

  const startDrag = (e) => {
    isDragging.value = false
    mouseDownPos.x = e.clientX
    mouseDownPos.y = e.clientY
  }

  const onDrag = (e) => {
    if (!isDragging.value) {
      // 计算移动距离
      const dx = Math.abs(e.clientX - mouseDownPos.x)
      const dy = Math.abs(e.clientY - mouseDownPos.y)
      // 只有当移动距离超过阈值时才认为是拖动
      if (dx > dragThreshold || dy > dragThreshold) {
        isDragging.value = true
      }
    }
  }

  const endDrag = () => {
    // 使用setTimeout确保点击事件在拖动结束后触发
    setTimeout(() => {
      isDragging.value = false
    }, 10)
  }

  const handleShowSender = () => {
    if (isDragging.value) return
    console.log('handleShowSender')
    fromQuestion.value = false
    showSender.value = !showSender.value
    inputValue.value = ''
    if (!showSender.value) {
      reply.value = ''
      handleCancel()
    }
  }

  lib.bus.busAIChat.on(async (question: string) => {
    console.log('AI机器人接收到：', question)
    fromQuestion.value = true
    // selectedTools.value = ''
    currentConversationId.value = ''
    lib.store().storeScreenData.isQuestionType = true

    if (question) {
      if (submitLoading.value) {
        handleCancel()
      }
      inputValue.value = question
      handleSubmit()
    }
  })
  onUnmounted(() => {
    lib.bus.busAIChat.reset()
    // handleClear()
  })

  const resultContentRef = ref(null)
  const scrollInterval = ref(null)
  const userScrolling = ref(false)
  const scrollTimeout = ref(null)

  const clear = () => {
    clearInterval(scrollInterval.value)
    scrollInterval.value = null
  }

  const clearScrollTimeout = () => {
    clearTimeout(scrollTimeout.value)
    scrollTimeout.value = null
  }

  const scrollToBottom = () => {
    const el = resultContentRef.value
    if (el) {
      el.scrollTop = el.scrollHeight
    }
  }

  const autoScroll = () => {
    // 只有在用户没有滚动时才自动滚动到底部
    if (!userScrolling.value) {
      scrollToBottom()
    }
  }

  // 检测用户是否在滚动
  const handleScroll = () => {
    userScrolling.value = true
    clearScrollTimeout()

    // 2秒后重置用户滚动状态，允许自动滚动
    scrollTimeout.value = setTimeout(() => {
      const el = resultContentRef.value
      if (el) {
        // 检查是否已经滚动到底部，如果是则恢复自动滚动
        const isAtBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 10
        if (isAtBottom) {
          userScrolling.value = false
        }
      }
    }, 2000)
  }

  // 监听 reply 变化，启动自动滚动
  watch(
    reply,
    () => {
      clear()
      if (reply.value) {
        // 立即滚动一次
        nextTick(() => {
          scrollToBottom()
        })
        // 启动定时器持续滚动（为了应对打字机效果）
        scrollInterval.value = setInterval(autoScroll, 100)
      }
    },
    { immediate: true }
  )

  // 监听 Typewriter 组件的内容变化，确保实时滚动
  const typerRef = ref(null)
  watch(
    () => typerRef.value?.displayedContent,
    () => {
      nextTick(() => {
        scrollToBottom()
      })
    },
    { flush: 'post' }
  )

  // 监听展开状态变化，重新滚动到底部
  watch(isZhanKai, () => {
    // 展开状态变化时，重置用户滚动状态并滚动到底部
    userScrolling.value = false
    clearScrollTimeout()
    nextTick(() => {
      scrollToBottom()
    })
  })

  // 监听加载状态，加载完成时清除定时器
  watch(submitLoading, (newVal) => {
    if (!newVal) {
      // 加载完成，等待一段时间后清除定时器
      setTimeout(() => {
        clear()
      }, 2000)
    }
  })

  // 组件销毁时清除定时器
  onUnmounted(() => {
    clear()
    clearScrollTimeout()
  })

  // #region mqtt

  const PublicMqtt = ref(null)
  const startMqtt = () => {
    // val = 订阅地址
    // 设置订阅地址
    PublicMqtt.value = new CreateMqtt('screenAi')
    // 初始化mqtt
    PublicMqtt.value.init()
    // 链接mqtt
    PublicMqtt.value.link()
    // isSendToUe.value = false
    getMessage()
  }
  // 获取订阅数据
  let listening = false //正在监听
  const getMessage = () => {
    PublicMqtt.value.client.on('message', (topic, message) => {
      const str = message.toString()
      console.log(str, '返回的数据')
      if (str.includes('检测到唤醒词')) {
        stopAudio()
        handleCancel()
        listening = true
        showSender.value = false
        reply.value = '我在'
        audioList.我在.play()
        lib.bus.busAIButtons.emit(false)
      }
      if (listening) {
        if (str.includes('实时识别')) {
          inputValue.value = str.slice(str.indexOf('实时识别:') + 5)
        } else if (str.includes('最终句子:')) {
          const str1 = str.slice(str.indexOf('最终句子:') + 5)
          if (str1.includes('作业') && str1.includes('场景') && (str1.includes('监管') || str1.includes('监控'))) {
            if (str1.includes('介绍')) {
              handleTest3()
            } else {
              handleTest2()
            }
          } else if (str1.includes('继续介绍')) {
            handleTest5()
          } else if (str1.includes('统计') && str1.includes('作业')) {
            handleTest4()
          } else if (str1.includes('建议')) {
            reply.value = ''
            submitLoading.value = true
            audioList.好的.play()
            setTimeout(() => {
              submitLoading.value = false
            }, 6000)
            lib.bus.busAIButtons.emit(true, str1)
          }
          // inputValue.value = str.slice(str.indexOf('最终句子:') + 5)
          listening = false
          // handleSubmit()
        }
      }
    })
  }
  // 取消MQTT订阅
  const unsubscribe = () => {
    // 如果页面并没有初始化MQTT，无需取消订阅
    if (PublicMqtt.value) {
      PublicMqtt.value.unsubscribes()
      PublicMqtt.value.over()
    }
  }

  onMounted(() => {
    startMqtt()
  })
  onUnmounted(() => {
    unsubscribe()
  })

  // #endregion

  const isTest = ref(false)
  const handleTest1 = () => {
    // isTest.value = !isTest.value
    // lib.bus.busYangHu.emit(isTest.value)
    stopAudio()
    showSender.value = false
    audioList.介绍.play()
    reply.value =
      '欢迎来到数字人的世界，我是您的贴身小助理“城市慧管家”，您可以随时呼叫我。在各位面前的是城市运营集团的数字化管理大屏。城市运营集团目前一共运营130余个项目，服务30个业主，除了上海，还服务了包括浙江在内的7个省市。'
    setTimeout(() => {
      const div = document.getElementById('ai-robot')
      div.style.left = '4400px'
      lib.bus.busMap.emit(false)
      lib.bus.busYangHu.emit(true, '浙江区域')
    }, 18000)
  }

  //慧管家，进入作业监管场景
  const handleTest2 = () => {
    stopAudio()
    showSender.value = false

    reply.value = '收到，这就打开作业监管场景'
    submitLoading.value = true
    audioList.场景2_1.play()

    setTimeout(() => {
      const div = document.getElementById('ai-robot')
      div.style.left = '4400px'
      submitLoading.value = false
      lib.bus.busYangHu.emit(true, '养护')
    }, 2000)
  }

  //慧管家，介绍一下作业监管场景
  const isIntroduceing = ref(false)
  const handleTest3 = () => {
    isIntroduceing.value = true
    stopAudio()
    showSender.value = false
    submitLoading.value = true
    reply.value = '好的，您可以通过呼叫“慧管家”随时打断我'
    audioList.场景2_2.play()

    setTimeout(() => {
      submitLoading.value = false
      reply.value =
        '在日常管理养护作业数字化监督和管理方面，智慧路网平台对作业端业务系统形成的计划、分配、执行和验收等日常作业管理数据进行实时监督与闭环管理。平台可视化呈现日常养护作业实时分析，帮助监管人员高效监管作业执行情况，并自动实时统计作业完成合格情况，包括安全交底合格率、计划执行率、作业路线的覆盖率等指标。'
      audioList.场景2_3.play()
    }, 6000)
  }

  //慧管家，帮我统计一下今天的作业总数
  const handleTest4 = () => {
    stopAudio()
    audioList.场景2_3.pause()
    showSender.value = false
    reply.value = '好的，马上统计'
    audioList.场景3_1.play()
    submitLoading.value = true

    setTimeout(() => {
      submitLoading.value = false
      reply.value = '今日总计作业124项'
      audioList.场景3_2.play()
    }, 3000)
  }

  //慧管家，继续介绍
  const handleTest5 = () => {
    stopAudio()
    audioList.场景2_3.play()
    showSender.value = false
    reply.value =
      '在日常管理养护作业数字化监督和管理方面，智慧路网平台对作业端业务系统形成的计划、分配、执行和验收等日常作业管理数据进行实时监督与闭环管理。平台可视化呈现日常养护作业实时分析，帮助监管人员高效监管作业执行情况，并自动实时统计作业完成合格情况，包括安全交底合格率、计划执行率、作业路线的覆盖率等指标。'
  }
  const stopAudio = () => {
    audioList.我在.pause()
    audioList.我在.currentTime = 0
    audioList.介绍.pause()
    audioList.介绍.currentTime = 0
    audioList.场景2_1.pause()
    audioList.场景2_1.currentTime = 0
    audioList.场景2_2.pause()
    audioList.场景2_2.currentTime = 0
    audioList.场景2_3.pause()
    // audioList.场景2_3.load()
    audioList.场景3_1.pause()
    audioList.场景3_1.currentTime = 0
  }

  const handleOpenAiWindow = () => {
    lib.bus.busAIButtons.emit(true, '查一下风机的维修建议')
  }

  onMounted(() => {
    lib.bus.busAiRobot.on((type: '首次加载' | '作业监管场景') => {
      console.log('type', type)
      if (type === '首次加载') {
        handleTest1()
      } else if (type === '作业监管场景') {
        handleTest2()
      }
    })
  })

  onUnmounted(() => {
    lib.bus.busAiRobot.reset()
  })
</script>

<style lang="scss">
  .ai-robot-container {
    position: absolute;
    top: 1000px;
    left: 2100px;
    width: 240px;
    height: 240px;
    .result-container {
      position: absolute;
      right: 200px;
      bottom: 200px;
      width: 675px;
      height: 423px;
      padding: 28px;
      font-size: 20px;
      line-height: 28px;
      color: #ffffff;
      background: rgb(6 84 174 / 30%);
      backdrop-filter: blur(6px);
      box-shadow: inset 0 0 6px 1px rgb(255 255 255 / 69%);
      transition: all 0.5s ease-in-out;
      .result-content {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        font-size: 32px;
        line-height: 40px;

        // /* 隐藏滚动条 */
        // &::-webkit-scrollbar {
        //   display: none;
        //   width: 0;
        // }
        &::-webkit-scrollbar {
          width: 6px;
          height: 6px;
          background-color: #999999;
        }
        &::-webkit-scrollbar-track {
          background-color: rgb(255 255 255 / 50%);
          border-radius: 10px;
          box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
        }
        &::-webkit-scrollbar-thumb {
          background-color: rgb(255 255 255 / 80%);
          border-radius: 10px;
          box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
        }
      }
      &.zhanKai {
        width: 1400px;
        height: 800px;
        font-size: 24px;
      }
    }
    .sender-container {
      @apply absolute bottom-20 -left-600 w600;

      background: rgb(255 255 255 / 50%);
      backdrop-filter: blur(10px);
      border: none;
      border: 1px solid #ffffff;
      border-radius: 20px;
      box-shadow: inset 0 0 22px 1px rgb(255 255 255 / 73%);
      .sender-style {
        .el-sender {
          border: none !important;
        }
        .el-textarea__inner::placeholder {
          color: #777777;
        }
        .el-textarea__inner {
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background-color: #999999;
          }
          &::-webkit-scrollbar-track {
            background-color: rgb(255 255 255 / 50%);
            border-radius: 10px;
            box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
          }
          &::-webkit-scrollbar-thumb {
            background-color: rgb(255 255 255 / 80%);
            border-radius: 10px;
            box-shadow: inset 0 0 3px rgb(0 0 0 / 30%);
          }
        }
      }
    }
    .tools-btns {
      display: flex;
      font-size: 20px;
      .selected {
        color: #0e1df0;
        border-color: #0e1df0;
      }
    }
  }
</style>
