/*
 * @Author: wang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-03-27 14:49:29
 * @LastEditors: wangjialing
 * @LastEditTime: 2025-06-25 15:19:43
 * @FilePath: \bigscreen-qj-web\src\utils\eventBus.js
 * @Description:
 *
 */
import { useEventBus } from '@vueuse/core'

/**
 * @description: EventBus统一管理
 */
export const bus = {
  /**
   * @description: 交通运行监测
   */
  trafficMonitor: useEventBus('trafficMonitor'),
  /**
   * @description: 交通运行监测图表数据
   */
  trafficMonitorChart: useEventBus('trafficMonitorChart'),
  /**
   * @description: 交通运行监测tab切换
   */
  trafficMonitorTab: useEventBus('trafficMonitorTab'),
  /**
   * @description: 点击事件类型echarts图获取处置效率图
   */
  busChangeEventType: useEventBus('changeEventType'),
  /**
   * @description: 态势感知-饼图数据
   */
  busMonitorRingChart: useEventBus('busMonitorRingChart'),
  /**
   * @description: 设备树形列表
   */
  busTreeTableStructure: useEventBus('busTreeTableStructure'),
  /**AI对话框 */
  busAIChat: useEventBus('busAIChat'),
  /**用来控制AI对话框的显示隐藏 */
  busAIButtons: useEventBus('busAIButtons'),
  /**养护专屏 */
  busYangHu: useEventBus('busYangHu'),
  busAiRobot: useEventBus('busAiRobot'),
  busMap: useEventBus('busMap')
}
