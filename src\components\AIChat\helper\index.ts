import { ref, computed, watch, onMounted } from 'vue'
import { useXStream } from 'vue-element-plus-x'
import { useAppState } from '@/composables/useAppState'
import { ElMessage } from 'element-plus'
// import type { BubbleListItemProps, BubbleListProps } from 'vue-element-plus-x/bubbleList/types'
// import mdTable from 'markdown-it-multimd-table'
// import MarkdownIt from 'markdown-it'
// import * * DifyChatbot from 'public/difysdk/index'

// 导入 FileItem 类型
import type { FileItem } from '../components/FileList/types'

// 临时类型定义
interface BubbleListItemProps {
  key?: number
  role?: 'user' | 'ai'
  content?: string
  timestamp?: string
  thinkingStatus?: 'start' | 'thinking' | 'end' | 'error'
  thinkingContent?: string
  quoteList?: any[]
  echartsJSONData?: any
  [key: string]: any
}

interface BubbleListProps<T = BubbleListItemProps> {
  list?: T[]
  maxHeight?: string
  [key: string]: any
}

/**
 * 文件类型映射
 */
const FILE_TYPE_EXTENSIONS = {
  document: ['TXT', 'MD', 'MDX', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOC', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
  image: ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
  audio: ['MP3', 'M4A', 'WAV', 'AMR', 'MPGA'],
  video: ['MP4', 'MOV', 'MPEG', 'WEBM']
}

/**
 * 根据文件类型获取支持的扩展名
 * @param allowedTypes 允许的文件类型数组
 * @returns 支持的文件扩展名数组
 */
function getSupportedExtensions(allowedTypes: string[]): string[] {
  const extensions: string[] = []
  allowedTypes.forEach((type) => {
    if (FILE_TYPE_EXTENSIONS[type]) {
      extensions.push(...FILE_TYPE_EXTENSIONS[type])
    }
  })
  return extensions
}

/**
 * 获取文件类型
 * @param fileName 文件名
 * @returns 文件类型
 */
function getFileType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toUpperCase()
  if (!extension) return 'unknown'

  for (const [type, extensions] of Object.entries(FILE_TYPE_EXTENSIONS)) {
    if (extensions.includes(extension)) {
      return type
    }
  }
  return 'unknown'
}

/**
 * 根据文件类型获取大小限制
 * @param fileType 文件类型
 * @param config 配置对象
 * @returns 大小限制(MB)
 */
function getFileSizeLimit(fileType: string, config: any): number {
  const typeToConfigMap = {
    image: 'image_file_size_limit',
    video: 'video_file_size_limit',
    audio: 'audio_file_size_limit',
    document: 'file_size_limit'
  }

  const configKey = typeToConfigMap[fileType]
  if (configKey && config[configKey]) {
    return config[configKey]
  }

  return config.file_size_limit || 50 // 默认50MB
}

function getDifyBaseUrl() {
  const localUrl = localStorage.getItem('difyBaseUrl')
  const difyBaseUrl = localUrl || window.config.difyAPIBaseUrl
  if (!localUrl) {
    localStorage.setItem('difyBaseUrl', difyBaseUrl)
  }
  return difyBaseUrl
}

/**
 * 生成一个 UUID v4
 *
 * 优先使用内置的 crypto.randomUUID（现代浏览器 & Node.js 20+ 支持），
 * 若不可用则回退到手动随机实现。
 */
function generateGUID() {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    // 浏览器或 Node.js 原生支持
    return crypto.randomUUID()
  }
  // 手动实现 UUID v4
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    // 对于 y 位置，取 r & 0x3 | 0x8，保证高位为 8、9、A 或 B
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

function getInputsForm() {
  let inputs = localStorage.getItem('inputs_form')
  if (inputs) {
    // 如果存在则直接返回
    return JSON.parse(inputs)
  }
  // 如果不存在则生成一个新的会话 ID
  const inputsObj = {}
  localStorage.setItem('inputs_form', JSON.stringify(inputsObj))
  // 生成一个新的会话 ID 这个 ID 可以用于跟踪本次用户所有的对话信息
  // console.log('session_id:', session_id);
  return inputsObj
}

function getSessionId() {
  let session_id = localStorage.getItem('dify_session_id')
  if (session_id) {
    // 如果存在则直接返回
    return session_id
  }
  // 如果不存在则生成一个新的会话 ID
  session_id = generateGUID()
  localStorage.setItem('dify_session_id', session_id)
  // 生成一个新的会话 ID 这个 ID 可以用于跟踪本次用户所有的对话信息
  console.log('session_id:', session_id)
  return session_id
}

function getCurrentConversationId() {
  let conversationId = localStorage.getItem('curConversationId')
  return conversationId || ''
}

function saveCurrentConversationIdToStore(conversationId: string) {
  if (conversationId === '' || conversationId === '-1') {
    localStorage.removeItem('curConversationId')
  } else {
    localStorage.setItem('curConversationId', conversationId)
  }
}

function getAppKeyMap() {
  const { getAppKeyMap } = useAppState()
  return getAppKeyMap()
}

function setAppKeyMap(appKeyMap: Record<string, string>) {
  const { setAppKeyMap } = useAppState()
  setAppKeyMap(appKeyMap)
}

function getCurrentAppKey() {
  const { getCurrentAppKey } = useAppState()
  return getCurrentAppKey()
}

function setCurrentAppKey(toolName: string) {
  const { setCurrentAppKey } = useAppState()
  setCurrentAppKey(toolName)
  return toolName
}

function getCurrentAppKeyValue() {
  const { getCurrentAppKeyValue } = useAppState()
  return getCurrentAppKeyValue()
}

/**
 * 上传文件到服务器
 * @param files 文件列表
 * @returns 上传结果
 */
async function uploadFilesToServer(files: File[]) {
  try {
    const currentAppKey = getCurrentAppKeyValue()
    if (!currentAppKey) {
      throw new Error('没有选中的应用')
    }

    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`files`, file)
    })

    // 使用标准的 fetch API 上传文件
    const response = await chatDifyInstance.fileUpload(files[0])
    if (response && response.id) {
      return {
        success: true,
        data: response.id,
        message: '文件上传成功'
      }
    } else {
      throw new Error('上传响应格式错误')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    return {
      success: false,
      error: error.message || '文件上传失败',
      message: '文件上传失败'
    }
  }
}

// @ts-ignore
console.log(' --- DifyChatbot --- ', window.DifyChatboxSDK)
// @ts-ignore
const chatDifyInstance = new window.DifyChatboxSDK.ChatClientWeb(getDifyBaseUrl(), '')
console.log(' chatDifyInstance --- ', chatDifyInstance)

// 将chatDifyInstance暴露给全局使用
;(window as any).chatDifyInstance = chatDifyInstance

// 定义全局的 listType
type listType = BubbleListItemProps & {
  key: number
  role: 'user' | 'ai'
  thinkingContent?: string //思考内容
  thinkingStatus?: 'start' | 'thinking' | 'end' | 'error' //思考状态
  quoteList?: any[] //引用列表
  echartsJSONData?: any //echartsJSONData
}

// 全局状态 - 使用模块级别的状态，保证在整个应用中唯一
const globalInputValue = ref('')
const globalRecording = ref(false)
const globalSubmitLoading = ref(false)
const globalCurrentConversationId = ref(getCurrentConversationId() || '')
const globalShowSender = ref(false)
const globalIsCarryHistory = ref(false)
const globalConversationList = ref([])
const globalCurrentConversationMessages = ref([])
const globalKey = ref(0)
const globalList = ref<listType[]>([])
// 全局文件上传状态
const globalUploadedFiles = ref<FileItem[]>([])

// 全局状态 - 处理当前对话的消息列表（仅用于新对话）
const globalCurrentList = ref<listType[]>([])

// 使用新的应用状态管理
const { currentAppKeyValue } = useAppState()
chatDifyInstance.setAppKey(currentAppKeyValue.value) // 初始化时设置 appKey

export const useAiChat = () => {
  console.log(' useAiChat --- ')

  // 使用全局状态替代本地状态
  const inputValue = globalInputValue
  const recording = globalRecording
  const submitLoading = globalSubmitLoading
  const currentConversationId = globalCurrentConversationId
  const showSender = globalShowSender
  const isCarryHistory = globalIsCarryHistory
  const conversationList = globalConversationList
  const currentConversationMessages = globalCurrentConversationMessages
  const key = globalKey
  const list = globalList
  const currentList = globalCurrentList // 新增：当前对话的消息列表
  const uploadedFiles = globalUploadedFiles // 新增：文件上传状态

  const { startStream, cancel, data, error, isLoading } = useXStream()

  /**
   * 将 API 返回的历史对话消息转换为 listType 格式
   */
  const convertMessagesToListType = (messages: any[]): listType[] => {
    const convertedMessages: listType[] = []
    let keyCounter = 0

    messages.forEach((message) => {
      // 用户消息
      if (message.query) {
        keyCounter++
        convertedMessages.push({
          key: keyCounter,
          role: 'user',
          placement: 'end',
          content: message.query,
          timestamp: new Date(message.created_at * 1000).toISOString()
        })
      }

      // AI 回复
      if (message.answer) {
        keyCounter++
        convertedMessages.push({
          key: keyCounter,
          role: 'ai',
          placement: 'start',
          content: message.answer,
          timestamp: new Date(message.created_at * 1000).toISOString(),
          loading: false,
          shape: 'corner',
          variant: 'filled',
          isMarkdown: true,
          maxWidth: '1700px',
          thinkingStatus: 'end',
          quoteList: message.retriever_resources || []
          // 如果有其他字段需要转换，可以在这里添加
        })
      }
    })

    return convertedMessages
  }

  // 更新全局 key 值，确保不与历史消息冲突
  const updateGlobalKey = () => {
    const historyMessages = currentConversationMessages.value.length > 0 ? convertMessagesToListType(currentConversationMessages.value) : []

    const maxHistoryKey = historyMessages.length > 0 ? Math.max(...historyMessages.map((m) => m.key)) : 0

    key.value = Math.max(key.value, maxHistoryKey)
  }

  /**
   * 合并历史对话和当前对话的计算属性
   */
  const displayList = computed(() => {
    const historyMessages = currentConversationMessages.value.length > 0 ? convertMessagesToListType(currentConversationMessages.value) : []

    // 合并历史消息和当前对话
    const totalMessages = [...historyMessages, ...currentList.value]
    console.log(' --- totalMessages --- ', totalMessages)
    return totalMessages
    // return [...historyMessages, ...currentList.value]
  })

  /**录音 */
  const handleRecord = () => {
    recording.value = !recording.value
    console.log('正在录音', recording.value)
  }

  /**取消提交 */
  const handleCancel = () => {
    if (submitLoading.value) {
      cancel()
      currentList.value = currentList.value.slice(0, currentList.value.length - 2)
      submitLoading.value = false
    }
  }

  /** 清空 */
  const handleClear = () => {
    console.log('handleClear')
    currentList.value = [] // 只清空当前对话
    data.value = [] // 清空流式数据
    currentConversationId.value = '-1'
    saveCurrentConversationIdToStore('-1')
    inputValue.value = ''
    submitLoading.value = false
    currentConversationMessages.value = [] // 清空当前对话的消息列表
    currentList.value = []
    uploadedFiles.value = [] // 清空上传文件
    cancel()
  }

  const handleThinkingChange = (payload: { value: boolean; status: 'start' | 'thinking' | 'end' | 'error' }) => {
    console.log('value', payload.value, 'status', payload.status)
  }

  /** 处理 appKey 变化 */
  const handleAppKeyChange = async (newAppKey: string) => {
    console.log('appKey 变化:', newAppKey)

    // 清空当前对话
    handleClear()

    // 1. 更新 chatDifyInstance 的 appKey
    chatDifyInstance.setAppKey(newAppKey)
    // 2. 重新获取应用参数
    await getAgentParameters()
    // 3. 重新获取对话列表
    await getConversationList()

    // 切换的时候 默认打开第一个对话
    if (conversationList.value && conversationList.value.length > 0) {
      // 如果有对话列表，默认选择第一个对话
      currentConversationId.value = conversationList.value[0].id || '-1'
      saveCurrentConversationIdToStore(currentConversationId.value)
      await handleCoversationIdChange(currentConversationId.value)
    }
  }

  /** 处理对话ID变化 */
  const handleCoversationIdChange = async (newConversationId: string) => {
    console.log('对话ID变化:', newConversationId)

    try {
      // 1. 把这个值存到 localStorage
      saveCurrentConversationIdToStore(newConversationId)

      // 2. 获取这个 conversationId 对应的对话消息列表
      if (newConversationId && newConversationId !== '-1') {
        const response = await chatDifyInstance.getConversationMessages(newConversationId)
        console.log('获取对话消息列表 response:', response)

        // 3. 保存当前对话的消息列表
        if (response && response.data) {
          currentConversationMessages.value = response.data
          if (response.data.length > 0) {
            // 从历史消息恢复 必传参数
            const lastMessage = response.data[response.data.length - 1]
            const inputs = lastMessage.inputs || {}
            chatDifyInstance.setInputs(inputs) // 设置 inputs 到 chatDifyInstance
          }
          currentConversationId.value = newConversationId
          // 清空当前对话，显示历史记录
          currentList.value = []
          // 更新 key 值，确保不与历史消息冲突
          updateGlobalKey()
        } else {
          // 4. 如果获取的值有问题，表示这个 conversationId 是错误的
          console.error('获取对话消息列表失败，conversationId 可能无效')
          currentConversationId.value = '-1'
          saveCurrentConversationIdToStore('-1')
          currentConversationMessages.value = []
          currentList.value = []
        }
      } else {
        // 清空当前对话
        currentConversationId.value = ''
        currentConversationMessages.value = []
        currentList.value = []
      }
    } catch (error) {
      console.error('获取对话消息列表时出错:', error)
      // 4. 如果出错，表示这个 conversationId 是错误的
      currentConversationId.value = '-1'
      saveCurrentConversationIdToStore('-1')
      currentConversationMessages.value = []
      currentList.value = []
    }
  }

  const handleSubmit = () => {
    console.log('提交')
    submitLoading.value = true

    // 确保 key 值不与历史消息冲突
    updateGlobalKey()

    key.value++
    currentList.value.push({
      key: key.value, // 唯一标识
      role: 'user', // user | ai 自行更据模型定义
      placement: 'end', // start | end 气泡位置
      content: inputValue.value // 消息内容 流式接受的时候，只需要改这个值即可
    })

    key.value++
    const newReply: listType = {
      key: key.value, // 唯一标识
      role: 'ai', // user | ai 自行更据模型定义
      placement: 'start', // start | end 气泡位置
      content: '', // 消息内容 流式接受的时候，只需要改这个值即可
      loading: true, // 当前气泡的加载状态
      shape: 'corner', // 气泡的形状
      variant: 'filled', // 气泡的样式
      isMarkdown: true, // 是否渲染为 markdown
      // mdPlugins, // 自定义 markdown 插件
      typing: true, // 是否开启打字器效果 该属性不会和流式接受冲突
      isFog: true, // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
      maxWidth: '1700px',
      thinkingContent: '',
      thinkingStatus: 'thinking'
    }

    currentList.value.push(newReply)

    startSSE(inputValue.value)

    // 提交后清空上传文件
    uploadedFiles.value = []
  }

  const getAgentParameters = async () => {
    try {
      const { getCurrentAppKeyValue, setAppParameters } = useAppState()
      const currentAppKey = getCurrentAppKeyValue()

      if (!currentAppKey) {
        console.warn('当前没有选中的 appKey')
        return null
      }

      console.log('获取应用参数:', currentAppKey)

      // 使用 chatDifyInstance 获取应用参数
      const response = await chatDifyInstance.getApplicationParameters()
      console.log('应用参数响应:', response)

      if (response) {
        // 将获取的参数保存到应用状态管理中
        setAppParameters(currentAppKey, response)
        return response
      } else {
        console.warn('获取应用参数失败，响应为空')
        return null
      }
    } catch (error) {
      console.error('获取应用参数时出错:', error)
      return null
    }
  }

  const startSSE = async (query) => {
    if (isCarryHistory.value) {
      query = historyStr.value + '\n\n\n\n' + query
      isCarryHistory.value = false
    }
    try {
      const session_id = getSessionId()
      const inputs = getInputsForm()

      let files = undefined
      if (globalUploadedFiles.value.length > 0) {
        files = globalUploadedFiles.value.map((file) => {
          if (file.isRemote) {
            return {
              type: file.type,
              transfer_method: 'remote_url', // 远程文件上传
              url: file.url, // 远程文件的 URL
              upload_file_id: '' // 使用上传的文件 ID
            }
          } else {
            return {
              type: file.type,
              transfer_method: 'local_file', // 本地文件上传
              url: '', // 本地文件没有 URL
              upload_file_id: file.uploadId // 使用上传的文件 ID
            }
          }
        })
      }
      // 下面是使用 Dify 的 API 进行流式请求的示例
      // const response = await fetch(`${getDifyBaseUrl()}/chat-messages`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer app-YqymE7Ts9slTS7EJ4HsOC1lE`, // 使用 appKey 作为 Bearer Token
      //   },
      //   body: JSON.stringify({
      //     query: query,
      //     response_mode: 'streaming',
      //     inputs: {},
      //     user: '1', // 这个地方的 user 需要业务层自己维护
      //     conversation_id: currentConversationId.value
      //     session_id: session_id, // 如果有会话 ID，可以传递
      //   })
      // })

      // 服务端默认会自动生成 建议前端自己生成 并且和用户数据关联 保存到服务器，这样可以把 对话记录绑定到用户
      // 生成一个新的会话 ID 这个 ID 可以用于跟踪本次用户所有的对话信息
      // 对话列表信息依赖于 appKey 和 session_id

      console.log('session_id:', session_id)

      // 下面是使用 NodeJS 中间层的 API 进行流式请求的示例
      // const response = await fetch(`${getDifyBaseUrl()}/chat-messages`, {
      //   method: 'POST',
      //   credentials: 'include',
      //   headers: { 'Content-Type': 'text/event-stream' },
      //   body: JSON.stringify({
      //     // app_key: window.config.appKey,
      //     app_key: appKey.value,
      //     query: query,
      //     response_mode: 'streaming',
      //     inputs,
      //     conversation_id: currentConversationId.value,
      //     session_id: session_id, // 如果有会话 ID，可以传递
      //   })
      // })

      // 设置会话 ID
      chatDifyInstance.setSessionId(session_id) // 设置了 session_id 后，所有的请求都会自动携带这个会话 ID
      const conversation_id = currentConversationId.value !== '-1' ? currentConversationId.value : ''
      const response = await chatDifyInstance.createChatMessage(inputs, query, 'stream', conversation_id, undefined, files)

      if (response && response.status !== 200) {
        // console.error('SSE 请求失败:', response.statusText)
        submitLoading.value = false
        console.log(response)
        const res = await response.json()
        console.error('SSE 请求失败:', res)
        if (res && res.message) {
          // pls use vue ui to show message
          // alert(`请求失败: ${response.statusText}`)
          ElMessage.error(`请求失败: ${res.message}`)
        }
        inputValue.value = ''
        submitLoading.value = false
        return
      }

      console.log('SSE response:', response)

      const readableStream = response.body!
      inputValue.value = ''

      const res = await startStream({ readableStream })
      console.log('SSE response:', res)
      submitLoading.value = false

      //结束后保存会话信息
      // if (currentConversationId.value === '') {
      getConversationList()
      // }
    } catch (err) {
      console.error('Fetch error:', err)
      if (err && err.message) {
        // pls use vue ui to show message
        // alert(`请求出错: ${err.message}`)
        console.error('请求出错:', err.message)
      }
    }
  }

  /**获取当前会话的conversation_id */
  const getConversationList = async () => {
    try {
      // dify 版本
      // const response = await fetch(`${getDifyBaseUrl()}/conversations?session_id=${session_id}`, {
      //   "headers": {
      //     "content-type": "application/json",
      //     "Authorization": `Bearer app-${appKey.value}`, // 使用 appKey 作为 Bearer Token
      //   },
      //   "body": null,
      //   "method": "GET",
      // });

      // nodejs 版本
      // const response = await fetch(`${getDifyBaseUrl()}/conversations?app_key=${appKey.value}&session_id=${session_id}`, {
      //   "headers": {
      //     "content-type": "application/json",
      //   },
      //   "body": null,
      //   "method": "GET",
      // });

      // js-sdk 版本
      const response = await chatDifyInstance.getConversations()
      console.log('获取会话列表 response:', response)

      // 将对话列表数据保存到 ref 中
      if (response && response.data) {
        // 使用扩展运算符创建新数组，确保引用变化触发响应式更新
        conversationList.value = [...response.data]
        console.log('对话列表更新:', conversationList.value)
      }
    } catch (error) {
      console.error('获取会话 ID 时出错:', error)
    }
  }

  const historyStr = computed(() => {
    const aiHistory = displayList.value.filter((item) => item.content !== '').slice(-10, -1)
    let num = 1

    return aiHistory
      .map((item, index) => {
        if (item.role === 'user') {
          return `历史提问${num}/5  ${item.content}`
        } else {
          num++
          return `历史回答${num - 1}/5  ${item.content}`
        }
      })
      .join('\n\n')
  })

  const content = computed(() => {
    if (!data.value.length) return ''
    let text = ''
    for (let index = 0; index < data.value.length; index++) {
      const chunk = data.value[index].data
      if (chunk) {
        try {
          const parsedChunk = JSON.parse(chunk)
          if (parsedChunk && parsedChunk.conversation_id) {
            // currentConversationId.value = parsedChunk.conversation_id
            if (currentConversationId.value !== parsedChunk.conversation_id) {
              console.log('对话ID变化:', parsedChunk.conversation_id)
              if (currentConversationId.value === '-1') {
                // 如果当前对话ID是 -1，表示是新对话，直接设置
                // 不需要
                console.log('新对话，设置对话ID 不需要 handleConversationIdChage : ', parsedChunk.conversation_id)
                currentConversationId.value = parsedChunk.conversation_id
                saveCurrentConversationIdToStore(parsedChunk.conversation_id)
              } else {
                currentConversationId.value = parsedChunk.conversation_id
                handleCoversationIdChange(parsedChunk.conversation_id)
              }
            }
          }
          if (parsedChunk.event === 'message') {
            text += parsedChunk.answer
          } else if (parsedChunk.event === 'agent_message') {
            text += parsedChunk.answer
          } else if (parsedChunk.event === 'error') {
            alert(parsedChunk.message)
            return 'error'
          }
        } catch (error) {
          console.error('解析数据时出错:', error)
        }
      }
    }
    return text
  })
  watch(content, (newVal) => {
    if (newVal) {
      console.log('newVal-------', newVal)
      const currentReply = currentList.value.find((_) => _.key === key.value) || {}
      if (newVal === 'error') {
        currentReply.thinkingStatus = 'error'
        currentReply.loading = false
        return
      }

      // 简化处理：直接将完整内容传递给 Answer 组件
      currentReply.content = newVal
      currentReply.loading = false
      currentReply.thinkingStatus = 'end'
    }
  })

  // 处理引用
  const quoteList = computed(() => {
    if (!data.value.length) return ''
    for (let index = 0; index < data.value.length; index++) {
      const chunk = data.value[index].data
      if (chunk) {
        try {
          const parsedChunk = JSON.parse(chunk)
          if (parsedChunk.event === 'message_end') {
            if (parsedChunk?.metadata?.retriever_resources?.length > 0) {
              return parsedChunk?.metadata?.retriever_resources.map((item) => {
                return {
                  title: item.document_name,
                  docId: item.document_id
                }
              })
            }
          }
        } catch (error) {
          console.error('解析文档列表时出错:', error)
        }
      }
    }
    return []
  })
  watch(quoteList, async (newVal) => {
    if (newVal?.length > 0) {
      console.log('引用列表-------', newVal)
      // const currentReply = currentList.value.find((_) => _.key === key.value)
      // const documentIdList = [...new Set(newVal.map((item) => item.docId))]
      // const res = await lib.api.knowledgeFileApi.getFromDocumentId({
      //   documentIdList: documentIdList
      // })
      // currentReply.quoteList = res?.result || []
    }
  })

  // 初始化：从 localStorage 恢复 conversationId
  const initConversationId = getCurrentConversationId()
  if (initConversationId && initConversationId !== '-1') {
    currentConversationId.value = initConversationId
    // 异步获取对话消息列表
    handleCoversationIdChange(initConversationId)
  }

  /** 重命名对话 */
  async function handleConversationRename(conversationId: string, newName: string) {
    try {
      // 这里应该调用 API 重命名对话
      const response = await chatDifyInstance.renameConversation(conversationId, newName)
      console.log('重命名对话:', conversationId, newName)

      // 重新获取对话列表
      await getConversationList()
    } catch (error) {
      console.error('重命名对话失败:', error)
    }
  }

  /** 删除对话 */
  async function handleConversationDelete(conversationId: string) {
    try {
      // 这里应该调用 API 删除对话
      const response = await chatDifyInstance.deleteConversation(conversationId)
      console.log('删除对话:', conversationId)

      // 如果删除的是当前对话，清空当前对话
      if (currentConversationId.value === conversationId) {
        handleClear()
      }

      // 重新获取对话列表
      await getConversationList()
    } catch (error) {
      console.error('删除对话失败:', error)
    }
  }

  /** 新建对话 */
  async function handleNewConversation() {
    try {
      // 清空当前对话
      handleClear()

      console.log('新建对话')

      // 可以在这里添加创建新对话的逻辑
      // 比如发送一个默认消息来创建对话
    } catch (error) {
      console.error('新建对话失败:', error)
    }
  }

  /** 处理文件上传 */
  async function handleFileUpload(files: File[]) {
    try {
      console.log('开始上传文件:', files)

      // 上传文件到服务器
      const uploadResult = await uploadFilesToServer(files)

      if (uploadResult.success) {
        console.log('文件上传成功:', uploadResult.data)
        ElMessage.success(uploadResult.message)
        return uploadResult.data
      } else {
        console.error('文件上传失败:', uploadResult.error)
        ElMessage.error(uploadResult.message)
        return null
      }
    } catch (error) {
      console.error('文件上传处理失败:', error)
      ElMessage.error('文件上传处理失败')
      return null
    }
  }

  /** 添加文件到上传列表 */
  const addUploadFile = (file: FileItem) => {
    uploadedFiles.value.push(file)
  }

  /** 从上传列表中移除文件 */
  const removeUploadFile = (uid: string) => {
    const index = uploadedFiles.value.findIndex((f) => f.uid === uid)
    if (index !== -1) {
      uploadedFiles.value.splice(index, 1)
    }
  }

  /** 更新文件状态 */
  const updateFileStatus = (uid: string, status: FileItem['status'], progress?: number, uploadId?: string) => {
    const index = uploadedFiles.value.findIndex((f) => f.uid === uid)
    if (index !== -1) {
      uploadedFiles.value[index].status = status
      if (progress !== undefined) {
        uploadedFiles.value[index].progress = progress
      }
      if (uploadId) {
        uploadedFiles.value[index].uploadId = uploadId
      }
    }
  }

  /** 清空所有上传文件 */
  const clearUploadFiles = () => {
    uploadedFiles.value = []
  }

  /** 验证文件 */
  const validateFile = (file: File) => {
    const { globalAppParameters } = useAppState()
    const fileType = getFileType(file.name)

    // 检查文件类型
    const allowedTypes = globalAppParameters.value?.file_upload?.allowed_file_types || []
    if (!allowedTypes.includes(fileType)) {
      ElMessage.error(`不支持的文件类型: ${file.name}`)
      return false
    }

    // 检查文件大小
    const appConfig = globalAppParameters.value?.file_upload?.fileUploadConfig
    if (appConfig) {
      const sizeLimit = getFileSizeLimit(fileType, appConfig)
      const fileSizeMB = file.size / (1024 * 1024)

      if (fileSizeMB > sizeLimit) {
        ElMessage.error(`文件大小超过限制: ${fileSizeMB.toFixed(2)}MB > ${sizeLimit}MB`)
        return false
      }
    }

    return true
  }

  /** 上传文件到服务器 */
  const uploadFileToServer = async (fileItem: FileItem) => {
    if (!fileItem.raw) return false

    try {
      // 更新状态为上传中
      updateFileStatus(fileItem.uid, 'uploading', 0)

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        const file = uploadedFiles.value.find((f) => f.uid === fileItem.uid)
        if (file && file.progress < 90) {
          updateFileStatus(fileItem.uid, 'uploading', file.progress + 10)
        }
      }, 100)

      // 调用Dify文件上传接口
      const response = await chatDifyInstance.fileUpload(fileItem.raw)

      clearInterval(progressInterval)

      if (response && response.id) {
        updateFileStatus(fileItem.uid, 'success', 100, response.id)
        ElMessage.success(`文件 ${fileItem.name} 上传成功`)
        return true
      } else {
        throw new Error('上传响应格式错误')
      }
    } catch (error) {
      updateFileStatus(fileItem.uid, 'error', 0)
      ElMessage.error(`文件 ${fileItem.name} 上传失败: ${error.message}`)
      return false
    }
  }

  // 创建返回对象
  const instance = {
    inputValue,
    recording,
    submitLoading,
    list: displayList, // 使用 displayList 替代原来的 list
    showSender,
    currentConversationId,
    isCarryHistory,
    conversationList,
    currentConversationMessages, // 新增：当前对话的消息列表
    uploadedFiles, // 新增：上传文件列表
    handleRecord,
    handleCancel,
    handleSubmit,
    handleClear,
    handleThinkingChange,
    handleAppKeyChange,
    handleCoversationIdChange, // 新增：处理对话ID变化的方法
    getConversationList,
    getAgentParameters, // 新增：获取应用参数的方法
    handleFileUpload, // 新增：文件上传处理方法
    // 新增：文件管理方法
    addUploadFile,
    removeUploadFile,
    updateFileStatus,
    clearUploadFiles,
    validateFile,
    uploadFileToServer,
    // 新增：支持 ConversationList 组件的方法
    handleConversationRename,
    handleConversationDelete,
    handleNewConversation
  }

  // 初始化：立即获取对话列表和应用参数
  onMounted(async () => {
    await getConversationList()
    await getAgentParameters()
  })

  return instance
}

// 导出 appKeyMap 相关的工具函数和 composable
export { getAppKeyMap, setAppKeyMap, getCurrentAppKey, setCurrentAppKey, getCurrentAppKeyValue, useAppState }

// 导出 AppParameters 类型
export type { AppParameters } from '../../../composables/useAppState'

// 导出文件上传函数
export { uploadFilesToServer }

// 导出文件类型相关工具函数
export { getSupportedExtensions, getFileType, getFileSizeLimit }
