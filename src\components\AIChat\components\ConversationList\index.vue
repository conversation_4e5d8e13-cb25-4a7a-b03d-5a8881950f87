<template>
  <div class="conversation-list-container">
    <Conversations
      v-model:active="activeConversationId"
      :items="conversationItems"
      :show-built-in-menu="true"
      :menu="menuItems"
      :label-key="'name'"
      :row-key="'id'"
      @change="handleConversationChange"
      @menu-command="handleMenuCommand"
    >
      <template #header>
        <div class="conversation-header">
          <h3>对话列表</h3>
          <el-button 
            type="primary" 
            size="small" 
            @click="handleNewConversation"
          >
            新建对话
          </el-button>
        </div>
      </template>
      
      <template #label="{ item }">
        <div class="conversation-item-content">
          <div class="conversation-info">
            <div class="conversation-name-wrapper">
              <span class="conversation-name">{{ item.name || '未命名对话' }}</span>
              <span class="conversation-time">{{ formatTime(item.updated_at) }}</span>
            </div>
            <div class="conversation-status">
              <el-tag 
                :type="getStatusType(item.status)" 
                size="small"
              >
                {{ getStatusText(item.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </template>
    </Conversations>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { Conversations } from 'vue-element-plus-x'
import { ElButton, ElTag, ElMessageBox } from 'element-plus'
// ...existing code...
import { Edit, Delete, ChatLineSquare } from '@element-plus/icons-vue'
import { markRaw } from 'vue'

// 定义 props
interface Props {
  conversationList: any[]
  activeConversationId?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  conversationList: () => [],
  activeConversationId: '',
  loading: false
})

// 定义 emits
interface Emits {
  (e: 'conversation-change', conversationId: string): void
  (e: 'conversation-rename', conversationId: string, newName: string): void
  (e: 'conversation-delete', conversationId: string): void
  (e: 'new-conversation'): void
  (e: 'update:activeConversationId', conversationId: string): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const activeConversationId = computed({
  get: () => props.activeConversationId,
  set: (value) => emit('update:activeConversationId', value)
})

// 转换对话列表数据格式以适配 XConversations 组件
const conversationItems = computed(() => {
  return props.conversationList.map(item => ({
    ...item,
    key: item.id, // 确保有 key 属性
    label: item.name || '未命名对话', // 确保有 label 属性
    disabled: item.status !== 'normal' // 非正常状态的对话项为禁用状态
  }))
})

// 菜单配置
const menuItems = computed(() => [
  {
    label: '重命名',
    key: 'rename',
    icon: markRaw(Edit),
    command: 'rename'
  },
  {
    label: '删除',
    key: 'delete',
    icon: markRaw(Delete),
    command: 'delete',
    menuItemHoverStyle: {
      color: 'red',
      backgroundColor: 'rgba(255, 0, 0, 0.1)'
    }
  }
])

// 处理对话切换
const handleConversationChange = (item: any) => {
  console.log('对话切换:', item)
  emit('conversation-change', item.id)
}

// 处理菜单命令
const handleMenuCommand = (command: string, item: any) => {
  console.log('菜单命令:', command, item)
  switch (command) {
    case 'rename':
      handleRenameConversation(item.id)
      break
    case 'delete':
      handleDeleteConversation(item.id)
      break
  }
}

// 处理重命名对话
const handleRenameConversation = (conversationId: string) => {
  const conversation = props.conversationList.find(item => item.id === conversationId)
  if (conversation) {
    ElMessageBox.prompt(
      '请输入新的对话名称:',
      '重命名对话',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: conversation.name || '未命名对话',
        inputPlaceholder: '新的对话名称',
        inputPattern: /^.{1,50}$/,
        inputErrorMessage: '名称不能为空且不超过50字符',
        showClose: false,
        customClass: 'rename-conversation-box',
        center: true
      }
    ).then(({ value }) => {
      if (value && value.trim()) {
        emit('conversation-rename', conversationId, value.trim())
      }
    }).catch(() => {
      // 用户取消，无需处理
    })
  }
}

// 处理删除对话
const handleDeleteConversation = (conversationId: string) => {
  const conversation = props.conversationList.find(item => item.id === conversationId)
  if (conversation) {
    ElMessageBox.confirm(
      `确定要删除对话 "${conversation.name || '未命名对话'}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
        showClose: false,
        customClass: 'delete-confirm-box'
      }
    ).then(() => {
      emit('conversation-delete', conversationId)
    }).catch(() => {
      // 用户取消，无需处理
    })
  }
}

// 处理新建对话
const handleNewConversation = () => {
  emit('new-conversation')
}

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000) // 假设时间戳是秒级
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 超过1天
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const currentYear = now.getFullYear()
  
  if (year === currentYear) {
    return `${month}-${day}`
  } else {
    return `${year}-${month}-${day}`
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'normal':
      return 'success'
    case 'error':
      return 'danger'
    case 'processing':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'normal':
      return '正常'
    case 'error':
      return '错误'
    case 'processing':
      return '处理中'
    default:
      return '未知'
  }
}
</script>

<style scoped lang="scss">
.conversation-list-container {
  width: 100%;
  height: 100%;
  
  .conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .conversation-item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 8px 0;
    
    .conversation-info {
      flex: 1;
      min-width: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .conversation-name-wrapper {
        flex: 1;
        min-width: 0;
        padding-right: 8px;
        
        .conversation-name {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          // margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .conversation-time {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .conversation-status {
        flex-shrink: 0;
        margin-left: 8px;
        
        .el-tag {
          border-radius: 4px;
        }
      }
    }
  }
}

// 深度选择器，用于修改 XConversations 组件的样式
:deep(.conversations-container) {
  .conversations-list {
    height: 400px; // 设置固定高度
    padding: 8px 0;
    
    .conversation-item {
      // padding: 8px 16px;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.active {
        background-color: #f0f9ff;
        border-left: 3px solid #409eff;
      }
      
      &.hovered {
        background-color: #f5f7fa;
      }
      
      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}
</style>
