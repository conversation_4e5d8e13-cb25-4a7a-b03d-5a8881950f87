'use strict';

var axios = require('axios');

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
/* global Reflect, Promise, SuppressedError, Symbol, Iterator */

var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
    return extendStatics(d, b);
};

function __extends(d, b) {
    if (typeof b !== "function" && b !== null)
        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}

var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

function __generator(thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
}

typeof SuppressedError === "function" ? SuppressedError : function (error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

var API_PREFIX = '/api';
var TIME_OUT = 100000;
var ContentType = {
    json: 'application/json',
    stream: 'text/event-stream',
    form: 'application/x-www-form-urlencoded; charset=UTF-8',
    download: 'application/octet-stream',
    multipart: 'multipart/form-data', // for file uploads
};
// Fix for fetch options
var baseOptions = {
    method: 'GET',
    mode: 'cors',
    credentials: 'include',
    headers: new Headers({
        'Content-Type': ContentType.json,
    }),
    redirect: 'follow',
};
function unicodeToChar(text) {
    return text.replace(/\\u[0-9a-f]{4}/g, function (_match, p1) {
        return String.fromCharCode(parseInt(p1, 16));
    });
}
// In the handleStream function, ensure it returns void
var handleStream = function (response, onData, onCompleted, onThought, onMessageEnd, onMessageReplace, onFile, onWorkflowStarted, onWorkflowFinished, onNodeStarted, onNodeFinished) {
    var _a;
    if (!response.ok)
        throw new Error('Network response was not ok');
    var reader = (_a = response.body) === null || _a === void 0 ? void 0 : _a.getReader();
    var decoder = new TextDecoder('utf-8');
    var buffer = '';
    var bufferObj;
    var isFirstMessage = true;
    function read() {
        var hasError = false;
        reader === null || reader === void 0 ? void 0 : reader.read().then(function (result) {
            if (result.done) {
                onCompleted && onCompleted();
                return;
            }
            buffer += decoder.decode(result.value, { stream: true });
            var lines = buffer.split('\n');
            try {
                lines.forEach(function (message) {
                    if (message.startsWith('data: ')) { // check if it starts with data:
                        try {
                            bufferObj = JSON.parse(message.substring(6)); // remove data: and parse as json
                        }
                        catch (e) {
                            // mute handle message cut off
                            onData('', isFirstMessage, {
                                conversationId: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.conversation_id,
                                messageId: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.message_id,
                            });
                            return;
                        }
                        if (bufferObj.status === 400 || !bufferObj.event) {
                            onData('', false, {
                                conversationId: undefined,
                                messageId: '',
                                errorMessage: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.message,
                                errorCode: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.code,
                            });
                            hasError = true;
                            onCompleted === null || onCompleted === void 0 ? void 0 : onCompleted(true);
                            return;
                        }
                        if (bufferObj.event === 'message' || bufferObj.event === 'agent_message') {
                            // can not use format here. Because message is splited.
                            onData(unicodeToChar(bufferObj.answer), isFirstMessage, {
                                conversationId: bufferObj.conversation_id,
                                taskId: bufferObj.task_id,
                                messageId: bufferObj.id,
                            });
                            isFirstMessage = false;
                        }
                        else if (bufferObj.event === 'agent_thought') {
                            onThought === null || onThought === void 0 ? void 0 : onThought(bufferObj);
                        }
                        else if (bufferObj.event === 'message_file') {
                            onFile === null || onFile === void 0 ? void 0 : onFile(bufferObj);
                        }
                        else if (bufferObj.event === 'message_end') {
                            onMessageEnd === null || onMessageEnd === void 0 ? void 0 : onMessageEnd(bufferObj);
                        }
                        else if (bufferObj.event === 'message_replace') {
                            onMessageReplace === null || onMessageReplace === void 0 ? void 0 : onMessageReplace(bufferObj);
                        }
                        else if (bufferObj.event === 'workflow_started') {
                            onWorkflowStarted === null || onWorkflowStarted === void 0 ? void 0 : onWorkflowStarted(bufferObj);
                        }
                        else if (bufferObj.event === 'workflow_finished') {
                            onWorkflowFinished === null || onWorkflowFinished === void 0 ? void 0 : onWorkflowFinished(bufferObj);
                        }
                        else if (bufferObj.event === 'node_started') {
                            onNodeStarted === null || onNodeStarted === void 0 ? void 0 : onNodeStarted(bufferObj);
                        }
                        else if (bufferObj.event === 'node_finished') {
                            onNodeFinished === null || onNodeFinished === void 0 ? void 0 : onNodeFinished(bufferObj);
                        }
                    }
                });
                buffer = lines[lines.length - 1];
            }
            catch (e) {
                onData('', false, {
                    conversationId: undefined,
                    messageId: '',
                    errorMessage: "".concat(e),
                });
                hasError = true;
                onCompleted === null || onCompleted === void 0 ? void 0 : onCompleted(true);
                return;
            }
            if (!hasError)
                read();
        });
    }
    read();
};
// In the baseFetch function, update the return Promise type
var baseFetch = function (url, fetchOptions, options) {
    var needAllResponseContent = options.needAllResponseContent, apiBaseUrl = options.apiBaseUrl;
    var requestOptions = Object.assign({}, baseOptions, fetchOptions);
    var urlPrefix = apiBaseUrl || API_PREFIX;
    var urlWithPrefix = "".concat(urlPrefix).concat(url.startsWith('/') ? url : "/".concat(url));
    var method = requestOptions.method, params = requestOptions.params, body = requestOptions.body;
    // handle query
    if ((method === 'GET' || method === 'DELETE') && params) {
        var paramsArray_1 = [];
        Object.keys(params).forEach(function (key) {
            return paramsArray_1.push("".concat(key, "=").concat(encodeURIComponent(params[key])));
        });
        if (urlWithPrefix.search(/\?/) === -1)
            urlWithPrefix += "?".concat(paramsArray_1.join('&'));
        else
            urlWithPrefix += "&".concat(paramsArray_1.join('&'));
        delete requestOptions.params;
    }
    if (body)
        requestOptions.body = JSON.stringify(body);
    // Handle timeout
    return Promise.race([
        new Promise(function (resolve, reject) {
            setTimeout(function () {
                return reject(new Error('request timeout'));
            }, TIME_OUT);
        }),
        new Promise(function (resolve, reject) {
            return globalThis.fetch(urlWithPrefix, requestOptions)
                .then(function (res) { return __awaiter(void 0, void 0, void 0, function () {
                var resClone, bodyJson, e_1, data;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            resClone = res.clone();
                            if (!!/^(2|3)\d{2}$/.test(res.status)) return [3 /*break*/, 4];
                            _a.label = 1;
                        case 1:
                            _a.trys.push([1, 3, , 4]);
                            return [4 /*yield*/, res.json()];
                        case 2:
                            bodyJson = _a.sent();
                            return [2 /*return*/, Promise.reject(bodyJson)];
                        case 3:
                            e_1 = _a.sent();
                            // const json = await res.json()
                            // console.log(json)
                            return [2 /*return*/, Promise.reject(e_1)];
                        case 4:
                            // handle delete api. Delete api not return content.
                            if (res.status === 204)
                                return [2 /*return*/, resolve({ result: 'success' })
                                    // return data
                                ];
                            data = requestOptions.headers.get('Content-type') === ContentType.download ? res.blob() : res.json();
                            return [2 /*return*/, resolve(needAllResponseContent ? resClone : data)];
                    }
                });
            }); })
                .catch(function (err) { return __awaiter(void 0, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    console.error(' baseFetch err ', err);
                    // const json = await res.json()
                    return [2 /*return*/, reject(err)];
                });
            }); });
        }),
    ]);
};
var upload = function (fetchOptions, options) {
    var apiBaseUrl = options.apiBaseUrl;
    var urlPrefix = apiBaseUrl || API_PREFIX;
    var urlWithPrefix = "".concat(urlPrefix, "/file-upload");
    var defaultOptions = {
        method: 'POST',
        url: "".concat(urlWithPrefix),
        data: {},
    };
    var requestOptions = __assign(__assign({}, defaultOptions), fetchOptions);
    return new Promise(function (resolve, reject) {
        var xhr = requestOptions.xhr;
        xhr.open(requestOptions.method, requestOptions.url);
        for (var key in requestOptions.headers)
            xhr.setRequestHeader(key, requestOptions.headers[key]);
        xhr.withCredentials = true;
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) {
                if (xhr.status === 200)
                    resolve({ id: xhr.response });
                else
                    reject(xhr);
            }
        };
        xhr.upload.onprogress = requestOptions.onprogress;
        xhr.send(requestOptions.data);
    });
};
var ssePost = function (url, fetchOptions, options) { return __awaiter(void 0, void 0, void 0, function () {
    var onData, onCompleted, onThought, onFile, onMessageEnd, onMessageReplace, onWorkflowStarted, onWorkflowFinished, onNodeStarted, onNodeFinished, onError, apiBaseUrl, requestOptions, urlPrefix, urlWithPrefix, body;
    return __generator(this, function (_a) {
        onData = options.onData, onCompleted = options.onCompleted, onThought = options.onThought, onFile = options.onFile, onMessageEnd = options.onMessageEnd, onMessageReplace = options.onMessageReplace, onWorkflowStarted = options.onWorkflowStarted, onWorkflowFinished = options.onWorkflowFinished, onNodeStarted = options.onNodeStarted, onNodeFinished = options.onNodeFinished, onError = options.onError, apiBaseUrl = options.apiBaseUrl;
        requestOptions = Object.assign({}, baseOptions, {
            method: 'POST',
        }, fetchOptions);
        urlPrefix = apiBaseUrl || API_PREFIX;
        urlWithPrefix = "".concat(urlPrefix).concat(url.startsWith('/') ? url : "/".concat(url));
        body = requestOptions.body;
        if (body)
            requestOptions.body = JSON.stringify(body);
        return [2 /*return*/, globalThis.fetch(urlWithPrefix, requestOptions)
                .then(function (res) {
                if (!/^(2|3)\d{2}$/.test(res.status)) {
                    res.json().then(function (data) {
                        onError === null || onError === void 0 ? void 0 : onError(data.message || 'Server Error', data.code);
                    }).catch(function () {
                        onError === null || onError === void 0 ? void 0 : onError('Server Error');
                    });
                    return;
                }
                return handleStream(res, function (str, isFirstMessage, moreInfo) {
                    if (moreInfo.errorMessage) {
                        onError === null || onError === void 0 ? void 0 : onError(moreInfo.errorMessage, moreInfo.errorCode);
                        return;
                    }
                    onData === null || onData === void 0 ? void 0 : onData(str, isFirstMessage, moreInfo);
                }, function () {
                    onCompleted === null || onCompleted === void 0 ? void 0 : onCompleted();
                }, onThought, onMessageEnd, onMessageReplace, onFile, onWorkflowStarted, onWorkflowFinished, onNodeStarted, onNodeFinished);
            }).catch(function (e) {
                onError === null || onError === void 0 ? void 0 : onError(e);
            })];
    });
}); };
var request = function (url, options, otherOptions) {
    if (options === void 0) { options = {}; }
    if (otherOptions === void 0) { otherOptions = {}; }
    return baseFetch(url, options, otherOptions);
};
var get = function (url, options, otherOptions) {
    if (options === void 0) { options = {}; }
    if (otherOptions === void 0) { otherOptions = {}; }
    return request(url, Object.assign({}, options, { method: 'GET' }), otherOptions);
};
var post = function (url, options, otherOptions) {
    if (options === void 0) { options = {}; }
    if (otherOptions === void 0) { otherOptions = {}; }
    return request(url, Object.assign({}, options, { method: 'POST' }), otherOptions);
};
var put = function (url, options, otherOptions) {
    if (options === void 0) { options = {}; }
    if (otherOptions === void 0) { otherOptions = {}; }
    return request(url, Object.assign({}, options, { method: 'PUT' }), otherOptions);
};
var del = function (url, options, otherOptions) {
    if (options === void 0) { options = {}; }
    if (otherOptions === void 0) { otherOptions = {}; }
    return request(url, Object.assign({}, options, { method: 'DELETE' }), otherOptions);
};
// Fix the postFormData function to properly handle options
var postFormData = function (url, formData, otherOptions) {
    if (otherOptions === void 0) { otherOptions = {}; }
    var needAllResponseContent = otherOptions.needAllResponseContent, apiBaseUrl = otherOptions.apiBaseUrl;
    var urlPrefix = apiBaseUrl || API_PREFIX;
    // console.log(apiBaseUrl, urlPrefix)
    var urlWithPrefix = "".concat(urlPrefix).concat(url.startsWith('/') ? url : "/".concat(url));
    // console.log(' haha ---  ', urlWithPrefix)
    var options = {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        body: formData,
        // Don't set Content-Type header, browser will set it with proper boundary
    };
    return Promise.race([
        new Promise(function (_, reject) {
            setTimeout(function () {
                reject(new Error('request timeout'));
            }, TIME_OUT);
        }),
        new Promise(function (resolve, reject) {
            globalThis.fetch(urlWithPrefix, options)
                .then(function (res) {
                var resClone = res.clone();
                // Error handler
                if (!/^(2|3)\d{2}$/.test(res.status.toString())) {
                    try {
                        return res.json().then(function (data) { return Promise.reject(data); });
                    }
                    catch (e) {
                        return Promise.reject(e);
                    }
                }
                // return data
                return res.json().then(function (data) {
                    resolve(needAllResponseContent ? resClone : data);
                });
            })
                .catch(function (err) {
                reject(err);
            });
        }),
    ]);
};

/**
 * Demo client for integrating with Dify Chatbox iframe
 * This file demonstrates how a parent application can communicate with an embedded Dify Chatbot
 */
/**
 * Example implementation of parent-side communication with Dify Chatbox
 */
var DifyChatboxComunication = /** @class */ (function () {
    /**
     * Initialize the Dify Chatbox client
     * @param iframeElement - The iframe element containing Dify Chatbox
     */
    function DifyChatboxComunication(iframeElement) {
        var _this = this;
        this.iframeReady = false;
        this.currentConversationId = null;
        this.messageHandlers = {};
        this.iframe = iframeElement;
        // this.iframe.src = chatboxUrl
        // Set up message handling
        this.setupMessageListener();
        // Create a function to send messages to the iframe
        this.sendToIframe = function (type, data) {
            if (!_this.iframe.contentWindow) {
                console.error('Cannot access iframe content window');
                return;
            }
            _this.iframe.contentWindow.postMessage({
                source: 'parent-app',
                type: type,
                data: data,
            }, '*'); // In production, specify the target origin instead of '*'
        };
    }
    /**
     * Set up event listener for messages from the iframe
     */
    DifyChatboxComunication.prototype.setupMessageListener = function () {
        var _this = this;
        window.addEventListener('message', function (event) {
            // In production, validate the origin
            // if (event.origin !== 'https://your-chatbox-domain.com') return;
            var _a = event.data || {}, source = _a.source, type = _a.type, data = _a.data;
            if (source === 'dify-chatbox') {
                // Handle specific message types
                switch (type) {
                    case 'ready':
                        _this.handleChatboxReady();
                        break;
                    case 'conversation-completed':
                        _this.handleConversationCompleted(data);
                        break;
                    case 'response-stream':
                        _this.handleResponseStream(data);
                        break;
                    case 'user-message-sent':
                        _this.handleUserMessageSent(data);
                        break;
                    case 'conversation-history':
                        _this.handleConversationHistory(data);
                        break;
                    case 'error':
                        _this.handleError(data);
                        break;
                    case 'feedback-updated':
                        _this.handleFeedbackUpdated(data);
                        break;
                }
                // Fire any registered custom handlers
                if (type && _this.messageHandlers[type])
                    _this.messageHandlers[type](data);
            }
        });
    };
    /**
     * Handle chatbox ready message
     */
    DifyChatboxComunication.prototype.handleChatboxReady = function () {
        this.iframeReady = true;
    };
    /**
     * Handle conversation completed message
     */
    DifyChatboxComunication.prototype.handleConversationCompleted = function (data) {
        console.log('Conversation completed:', data);
        this.currentConversationId = data.conversationId;
        // Example: Log or store the conversation
    };
    /**
     * Handle streaming response chunks
     */
    DifyChatboxComunication.prototype.handleResponseStream = function (data) {
        console.log('Received response chunk:', data.isFirstChunk ? '(First chunk)' : '(continuation)');
        // Example: Update UI with streaming response
    };
    /**
     * Handle user message sent notification
     */
    DifyChatboxComunication.prototype.handleUserMessageSent = function (data) {
        console.log('User sent message:', data.message);
        // Example: Update UI to show the message was sent
    };
    /**
     * Handle conversation history
     */
    DifyChatboxComunication.prototype.handleConversationHistory = function (data) {
        console.log('Received conversation history:', data.history);
        // Example: Display history in UI
    };
    /**
     * Handle error message
     */
    DifyChatboxComunication.prototype.handleError = function (data) {
        console.error('Chatbox error:', data.message, 'Code:', data.code);
        // Example: Show error in UI
    };
    /**
     * Handle feedback update
     */
    DifyChatboxComunication.prototype.handleFeedbackUpdated = function (data) {
        console.log('Feedback updated for message:', data.messageId, 'Rating:', data.rating);
        // Example: Update UI to reflect feedback
    };
    /**
     * Send a message to the chatbox
     * @param query - The message text
     * @param files - Optional files to attach
     * @param inputs - Optional input variables
     */
    DifyChatboxComunication.prototype.sendMessage = function (_a) {
        var query = _a.query, files = _a.files, inputs = _a.inputs;
        if (!this.iframeReady) {
            console.warn('Chatbox is not ready yet');
            return;
        }
        this.sendToIframe('send-message', {
            message: query,
            files: files,
            inputs: inputs,
        });
    };
    /**
     * Clear the conversation
     */
    DifyChatboxComunication.prototype.clearConversation = function () {
        if (!this.iframeReady) {
            console.warn('Chatbox is not ready yet');
            return;
        }
        this.sendToIframe('clear-conversation', {});
        this.currentConversationId = null;
    };
    /**
     * Set input variables
     * @param inputs - Key-value pairs of input variables
     */
    DifyChatboxComunication.prototype.setInputs = function (inputs) {
        if (!this.iframeReady) {
            console.warn('Chatbox is not ready yet');
            return;
        }
        this.sendToIframe('set-inputs', inputs);
    };
    /**
     * Request conversation history
     */
    DifyChatboxComunication.prototype.getConversationHistory = function () {
        if (!this.iframeReady) {
            console.warn('Chatbox is not ready yet');
            return;
        }
        this.sendToIframe('get-conversation-history', {});
    };
    /**
     * Get current status
     */
    DifyChatboxComunication.prototype.getStatus = function () {
        if (!this.iframeReady) {
            console.warn('Chatbox is not ready yet');
            return;
        }
        this.sendToIframe('get-status', {});
    };
    /**
     * Set interface language
     * @param language - Language code
     */
    DifyChatboxComunication.prototype.setLanguage = function (language) {
        if (!this.iframeReady) {
            console.warn('Chatbox is not ready yet');
            return;
        }
        this.sendToIframe('set-language', { language: language });
    };
    /**
     * Register a custom handler for a message type
     * @param type - Message type to handle
     * @param handler - Callback function
     */
    DifyChatboxComunication.prototype.on = function (type, handler) {
        this.messageHandlers[type] = handler;
    };
    return DifyChatboxComunication;
}());

/**
 * Dify Chatbot SDK for embedding a chat interface in any web application
 * This class handles the creation and management of the chatbot UI and communication with the iframe
 */
var DifyChatbot = /** @class */ (function () {
    /**
     * Create a new Dify Chatbot instance
     * @param options Configuration options for the chatbot
     */
    function DifyChatbot(options) {
        this.iframe = null;
        this.bubbleButton = null;
        this.bubbleWindow = null;
        this.isOpen = false;
        this.communicationClient = null;
        this.initialPosition = { x: 20, y: 20 };
        this.dragOffset = { x: 0, y: 0 };
        this.isDragging = false;
        // Set default values for optional parameters
        this.options = __assign({ CHATBOT_CONFIG_NAME: 'difyChatbotConfig', BUBBLE_BUTTON_ID: 'dify-chatbot-bubble-button', BUBBLE_WINDOW_ID: 'dify-chatbot-bubble-window', draggable: false, ICONS: {
                open: "<svg id=\"openIcon\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.7586 2L16.2412 2C17.0462 1.99999 17.7105 1.99998 18.2517 2.04419C18.8138 2.09012 19.3305 2.18868 19.8159 2.43598C20.5685 2.81947 21.1804 3.43139 21.5639 4.18404C21.8112 4.66937 21.9098 5.18608 21.9557 5.74818C21.9999 6.28937 21.9999 6.95373 21.9999 7.7587L22 14.1376C22.0004 14.933 22.0007 15.5236 21.8636 16.0353C21.4937 17.4156 20.4155 18.4938 19.0352 18.8637C18.7277 18.9461 18.3917 18.9789 17.9999 18.9918L17.9999 20.371C18 20.6062 18 20.846 17.9822 21.0425C17.9651 21.2305 17.9199 21.5852 17.6722 21.8955C17.3872 22.2525 16.9551 22.4602 16.4983 22.4597C16.1013 22.4593 15.7961 22.273 15.6386 22.1689C15.474 22.06 15.2868 21.9102 15.1031 21.7632L12.69 19.8327C12.1714 19.4178 12.0174 19.3007 11.8575 19.219C11.697 19.137 11.5262 19.0771 11.3496 19.0408C11.1737 19.0047 10.9803 19 10.3162 19H7.75858C6.95362 19 6.28927 19 5.74808 18.9558C5.18598 18.9099 4.66928 18.8113 4.18394 18.564C3.43129 18.1805 2.81937 17.5686 2.43588 16.816C2.18859 16.3306 2.09002 15.8139 2.0441 15.2518C1.99988 14.7106 1.99989 14.0463 1.9999 13.2413V7.75868C1.99989 6.95372 1.99988 6.28936 2.0441 5.74818C2.09002 5.18608 2.18859 4.66937 2.43588 4.18404C2.81937 3.43139 3.43129 2.81947 4.18394 2.43598C4.66928 2.18868 5.18598 2.09012 5.74808 2.04419C6.28927 1.99998 6.95364 1.99999 7.7586 2ZM10.5073 7.5C10.5073 6.67157 9.83575 6 9.00732 6C8.1789 6 7.50732 6.67157 7.50732 7.5C7.50732 8.32843 8.1789 9 9.00732 9C9.83575 9 10.5073 8.32843 10.5073 7.5ZM16.6073 11.7001C16.1669 11.3697 15.5426 11.4577 15.2105 11.8959C15.1488 11.9746 15.081 12.0486 15.0119 12.1207C14.8646 12.2744 14.6432 12.4829 14.3566 12.6913C13.7796 13.111 12.9818 13.5001 12.0073 13.5001C11.0328 13.5001 10.235 13.111 9.65799 12.6913C9.37138 12.4829 9.15004 12.2744 9.00274 12.1207C8.93366 12.0486 8.86581 11.9745 8.80418 11.8959C8.472 11.4577 7.84775 11.3697 7.40732 11.7001C6.96549 12.0314 6.87595 12.6582 7.20732 13.1001C7.20479 13.0968 7.21072 13.1043 7.22094 13.1171C7.24532 13.1478 7.29407 13.2091 7.31068 13.2289C7.36932 13.2987 7.45232 13.3934 7.55877 13.5045C7.77084 13.7258 8.08075 14.0172 8.48165 14.3088C9.27958 14.8891 10.4818 15.5001 12.0073 15.5001C13.5328 15.5001 14.735 14.8891 15.533 14.3088C15.9339 14.0172 16.2438 13.7258 16.4559 13.5045C16.5623 13.3934 16.6453 13.2987 16.704 13.2289C16.7333 13.1939 16.7567 13.165 16.7739 13.1432C17.1193 12.6969 17.0729 12.0493 16.6073 11.7001ZM15.0073 6C15.8358 6 16.5073 6.67157 16.5073 7.5C16.5073 8.32843 15.8358 9 15.0073 9C14.1789 9 13.5073 8.32843 13.5073 7.5C13.5073 6.67157 14.1789 6 15.0073 6Z\" fill=\"white\"/>\n        </svg>",
                close: "<svg id=\"closeIcon\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M18 18L6 6M6 18L18 6\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        </svg>",
            }, bubbleWindowWidth: 380, bubbleWindowHeight: 600 }, options);
        // Initialize the chatbot
        this.init();
    }
    /**
     * Initialize the chatbot UI elements
     */
    DifyChatbot.prototype.init = function () {
        this.createBubbleButton();
        this.attachButtonEvents();
    };
    /**
     * Create the chat bubble button element
     */
    DifyChatbot.prototype.createBubbleButton = function () {
        if (typeof document === 'undefined')
            return;
        // Create the bubble button if it doesn't exist
        if (!document.getElementById(this.options.BUBBLE_BUTTON_ID)) {
            this.bubbleButton = document.createElement('div');
            this.bubbleButton.id = this.options.BUBBLE_BUTTON_ID;
            this.bubbleButton.innerHTML = this.options.ICONS.open;
            this.bubbleButton.style.cssText = "\n        position: fixed;\n        bottom: 20px;\n        right: 20px;\n        width: 60px;\n        height: 60px;\n        border-radius: 50%;\n        background-color: #5e5eff;\n        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        cursor: pointer;\n        z-index: 9999;\n        transition: all 0.3s ease;\n      ";
            document.body.appendChild(this.bubbleButton);
        }
        else {
            this.bubbleButton = document.getElementById(this.options.BUBBLE_BUTTON_ID);
        }
    };
    /**
     * Create the chat window element
     */
    DifyChatbot.prototype.createBubbleWindow = function () {
        if (typeof document === 'undefined' || this.bubbleWindow)
            return;
        this.bubbleWindow = document.createElement('div');
        this.bubbleWindow.id = this.options.BUBBLE_WINDOW_ID;
        var width = typeof this.options.bubbleWindowWidth === 'number' ? "".concat(this.options.bubbleWindowWidth, "px") : (this.options.bubbleWindowWidth || '380px');
        var height = typeof this.options.bubbleWindowHeight === 'number' ? "".concat(this.options.bubbleWindowHeight, "px") : (this.options.bubbleWindowHeight || '600px');
        this.bubbleWindow.style.cssText = "\n      position: fixed;\n      bottom: 90px;\n      right: 20px;\n      width: ".concat(width, ";\n      height: ").concat(height, ";\n      background-color: #fff;\n      border-radius: 16px;\n      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);\n      overflow: hidden;\n      z-index: 9998;\n      transition: all 0.3s ease;\n      display: none;\n    ");
        if (this.options.draggable) {
            this.bubbleWindow.style.cursor = 'move';
            this.attachDragEvents();
        }
        document.body.appendChild(this.bubbleWindow);
    };
    /**
     * Create the iframe for the chatbot
     */
    DifyChatbot.prototype.createIframe = function () {
        if (!this.bubbleWindow || this.iframe)
            return;
        // Create unique ID for this chatbot instance
        var instanceId = "dify-chatbot-".concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9));
        this.iframe = document.createElement('iframe');
        this.iframe.style.cssText = "\n      width: 100%;\n      height: 100%;\n      border: none;\n    ";
        // Construct the iframe URL with the app key
        var iframeUrl = new URL("".concat(this.options.baseUrl, "?appKey=").concat(this.options.appKey));
        iframeUrl.searchParams.append('embed', 'true');
        iframeUrl.searchParams.append('iframe_id', instanceId);
        this.iframe.src = iframeUrl.toString();
        this.bubbleWindow.appendChild(this.iframe);
        // Initialize communication with iframe
        this.initIframeCommunication();
    };
    /**
     * Initialize communication with the iframe
     */
    DifyChatbot.prototype.initIframeCommunication = function () {
        if (!this.iframe)
            return;
        this.communicationClient = new DifyChatboxComunication(this.iframe);
    };
    /**
     * Attach events to the bubble button
     */
    DifyChatbot.prototype.attachButtonEvents = function () {
        var _this = this;
        if (!this.bubbleButton)
            return;
        this.bubbleButton.addEventListener('click', function () {
            _this.toggleChatWindow();
        });
    };
    /**
     * Toggle the chat window visibility
     */
    DifyChatbot.prototype.toggleChatWindow = function () {
        this.isOpen = !this.isOpen;
        if (this.isOpen) {
            if (!this.bubbleWindow)
                this.createBubbleWindow();
            if (!this.iframe)
                this.createIframe();
            if (this.bubbleWindow)
                this.bubbleWindow.style.display = 'block';
            if (this.bubbleButton)
                this.bubbleButton.innerHTML = this.options.ICONS.close;
        }
        else {
            if (this.bubbleWindow)
                this.bubbleWindow.style.display = 'none';
            if (this.bubbleButton)
                this.bubbleButton.innerHTML = this.options.ICONS.open;
        }
    };
    /**
     * Attach drag events to the chat window if draggable is enabled
     */
    DifyChatbot.prototype.attachDragEvents = function () {
        var _this = this;
        if (!this.bubbleWindow || !this.options.draggable)
            return;
        this.bubbleWindow.addEventListener('mousedown', function (e) {
            _this.isDragging = true;
            _this.dragOffset.x = e.clientX - _this.bubbleWindow.getBoundingClientRect().left;
            _this.dragOffset.y = e.clientY - _this.bubbleWindow.getBoundingClientRect().top;
            var mouseMoveHandler = function (e) {
                if (!_this.isDragging)
                    return;
                var x = e.clientX - _this.dragOffset.x;
                var y = e.clientY - _this.dragOffset.y;
                // Ensure the window stays within viewport bounds
                var maxX = window.innerWidth - _this.bubbleWindow.offsetWidth;
                var maxY = window.innerHeight - _this.bubbleWindow.offsetHeight;
                var boundedX = Math.max(0, Math.min(x, maxX));
                var boundedY = Math.max(0, Math.min(y, maxY));
                _this.bubbleWindow.style.left = "".concat(boundedX, "px");
                _this.bubbleWindow.style.right = 'auto';
                _this.bubbleWindow.style.top = "".concat(boundedY, "px");
                _this.bubbleWindow.style.bottom = 'auto';
            };
            var mouseUpHandler = function () {
                _this.isDragging = false;
                document.removeEventListener('mousemove', mouseMoveHandler);
                document.removeEventListener('mouseup', mouseUpHandler);
            };
            document.addEventListener('mousemove', mouseMoveHandler);
            document.addEventListener('mouseup', mouseUpHandler);
        });
    };
    /**
     * Send a message to the chatbot
     * @param query The message to send
     * @param files Optional files to attach
     * @param inputs Optional input variables
     */
    DifyChatbot.prototype.sendMessage = function (_a) {
        var query = _a.query, files = _a.files, inputs = _a.inputs;
        if (!this.communicationClient) {
            console.warn('Chatbot is not initialized yet');
            return;
        }
        this.communicationClient.sendMessage({ query: query, files: files, inputs: inputs });
    };
    /**
     * Clear the current conversation
     */
    DifyChatbot.prototype.clearConversation = function () {
        if (!this.communicationClient) {
            console.warn('Chatbot is not initialized yet');
            return;
        }
        this.communicationClient.clearConversation();
    };
    /**
     * Set input variables for the conversation
     * @param inputs Key-value pairs of input variables
     */
    DifyChatbot.prototype.setInputs = function (inputs) {
        if (!this.communicationClient) {
            console.warn('Chatbot is not initialized yet');
            return;
        }
        this.communicationClient.setInputs(inputs);
    };
    /**
     * Request the conversation history
     */
    DifyChatbot.prototype.getConversationHistory = function () {
        if (!this.communicationClient) {
            console.warn('Chatbot is not initialized yet');
            return;
        }
        this.communicationClient.getConversationHistory();
    };
    /**
     * Set the interface language
     * @param language The language code
     */
    DifyChatbot.prototype.setLanguage = function (language) {
        if (!this.communicationClient) {
            console.warn('Chatbot is not initialized yet');
            return;
        }
        this.communicationClient.setLanguage(language);
    };
    /**
     * Register a custom event handler
     * @param {MessageTypeEnum} type The message type to handle
     * @param handler The callback function
     */
    DifyChatbot.prototype.on = function (type, handler) {
        if (!this.communicationClient) {
            console.warn('Chatbot is not initialized yet');
            return;
        }
        this.communicationClient.on(type, handler);
    };
    /**
     * Open the chat window
     */
    DifyChatbot.prototype.open = function () {
        if (!this.isOpen)
            this.toggleChatWindow();
    };
    /**
     * Close the chat window
     */
    DifyChatbot.prototype.close = function () {
        if (this.isOpen)
            this.toggleChatWindow();
    };
    /**
     * Destroy the chatbot instance and clean up resources
     */
    DifyChatbot.prototype.destroy = function () {
        if (this.bubbleButton && this.bubbleButton.parentNode)
            this.bubbleButton.parentNode.removeChild(this.bubbleButton);
        if (this.bubbleWindow && this.bubbleWindow.parentNode)
            this.bubbleWindow.parentNode.removeChild(this.bubbleWindow);
        this.iframe = null;
        this.bubbleButton = null;
        this.bubbleWindow = null;
        this.communicationClient = null;
    };
    return DifyChatbot;
}());

var BASE_URL = 'https://api.dify.ai/v1';
// Routes for the Dify API
var routes$1 = {
    application: {
        method: 'GET',
        url: function () { return '/parameters'; },
    },
    feedback: {
        method: 'POST',
        url: function (message_id) { return "/messages/".concat(message_id, "/feedbacks"); },
    },
    createCompletionMessage: {
        method: 'POST',
        url: function () { return '/completion-messages'; },
    },
    createChatMessage: {
        method: 'POST',
        url: function () { return '/chat-messages'; },
    },
    getConversationMessages: {
        method: 'GET',
        url: function () { return '/messages'; },
    },
    getConversations: {
        method: 'GET',
        url: function () { return '/conversations'; },
    },
    renameConversation: {
        method: 'POST',
        url: function (conversation_id) { return "/conversations/".concat(conversation_id, "/name"); },
    },
    deleteConversation: {
        method: 'DELETE',
        url: function (conversation_id) { return "/conversations/".concat(conversation_id); },
    },
    fileUpload: {
        method: 'POST',
        url: function () { return '/files/upload'; },
    },
    runWorkflow: {
        method: 'POST',
        url: function () { return '/workflows/run'; },
    },
    audioToText: {
        method: 'POST',
        url: function () { return '/audio-to-text'; },
    },
};
/**
 * Base client for interacting with the Dify API
 * This class is designed to be used in Node.js environments
 * since exposing API keys in browsers is not secure
 */
var DifyClientServer = /** @class */ (function () {
    /**
     * Create a new Dify client
     *
     * @param apiKey - Your Dify API key
     * @param baseUrl - The Dify API base URL, defaults to https://api.dify.ai/v1
     */
    function DifyClientServer(apiKey, baseUrl) {
        if (baseUrl === void 0) { baseUrl = BASE_URL; }
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;
    }
    /**
     * Update the API key used by the client
     *
     * @param apiKey - Your new Dify API key
     */
    DifyClientServer.prototype.updateApiKey = function (apiKey) {
        this.apiKey = apiKey;
    };
    /**
     * Send a request to the Dify API
     *
     * @param method - HTTP method (GET, POST, etc.)
     * @param endpoint - API endpoint
     * @param data - Request payload for POST, PUT requests
     * @param params - Query parameters for the request
     * @param stream - Whether to request a streaming response
     * @param headerParams - Additional headers to include in the request
     * @returns Response from the Dify API
     */
    DifyClientServer.prototype.sendRequest = function (method, endpoint, data, params, stream, headerParams) {
        if (data === void 0) { data = null; }
        if (params === void 0) { params = null; }
        if (stream === void 0) { stream = false; }
        if (headerParams === void 0) { headerParams = {}; }
        return __awaiter(this, void 0, void 0, function () {
            var headers, url, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        headers = __assign({
                            'Authorization': "Bearer ".concat(this.apiKey),
                            'Content-Type': 'application/json',
                        }, headerParams);
                        url = "".concat(this.baseUrl).concat(endpoint);
                        if (!stream) return [3 /*break*/, 2];
                        return [4 /*yield*/, axios({
                                method: method,
                                url: url,
                                data: data,
                                params: params,
                                headers: headers,
                                responseType: 'stream',
                            })];
                    case 1:
                        response = _a.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, axios(__assign(__assign({ method: method, url: url }, (method !== 'GET' && { data: data })), { params: params, headers: headers, responseType: 'json' }))];
                    case 3:
                        response = _a.sent();
                        _a.label = 4;
                    case 4: return [2 /*return*/, response];
                }
            });
        });
    };
    /**
     * Send feedback for a message
     *
     * @param message_id - ID of the message to provide feedback for
     * @param rating - Feedback rating ('like' or 'dislike')
     * @param user - User identifier
     * @returns Response from the Dify API
     */
    DifyClientServer.prototype.messageFeedback = function (message_id, rating, user) {
        var data = {
            rating: rating,
            user: user,
        };
        return this.sendRequest(routes$1.feedback.method, routes$1.feedback.url(message_id), data);
    };
    /**
     * Get application parameters
     *
     * @param user - User identifier
     * @returns Application parameters from the Dify API
     */
    DifyClientServer.prototype.getApplicationParameters = function (user) {
        var params = { user: user };
        return this.sendRequest(routes$1.application.method, routes$1.application.url(), null, params);
    };
    /**
     * Upload a file to the Dify API
     *
     * @param data - Form data containing the file to upload
     * @returns Response from the Dify API
     */
    DifyClientServer.prototype.fileUpload = function (data) {
        return this.sendRequest(routes$1.fileUpload.method, routes$1.fileUpload.url(), data, null, false, {
            'Content-Type': 'multipart/form-data',
        });
    };
    /**
     * Convert audio to text
     *
     * @param data - Form data containing the audio file
     * @returns Transcription response from the Dify API
     */
    DifyClientServer.prototype.audioToText = function (data) {
        return this.sendRequest(routes$1.audioToText.method, routes$1.audioToText.url(), data, null, false, {
            'Content-Type': 'multipart/form-data',
        });
    };
    return DifyClientServer;
}());
/**
 * Client for interacting with the Dify Completion API
 */
var CompletionClientServer = /** @class */ (function (_super) {
    __extends(CompletionClientServer, _super);
    function CompletionClientServer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * Create a completion message
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param files - Files to include with the request
     * @returns Response from the Dify API
     */
    CompletionClientServer.prototype.createCompletionMessage = function (inputs, user, stream, files) {
        if (stream === void 0) { stream = false; }
        if (files === void 0) { files = null; }
        var data = {
            inputs: inputs,
            user: user,
            response_mode: stream ? 'streaming' : 'blocking',
            files: files,
        };
        return this.sendRequest(routes$1.createCompletionMessage.method, routes$1.createCompletionMessage.url(), data, null, stream);
    };
    /**
     * Run a workflow
     *
     * @param inputs - Input parameters for the workflow
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param files - Files to include with the request
     * @returns Response from the Dify API
     */
    CompletionClientServer.prototype.runWorkflow = function (inputs, user, stream, files) {
        if (stream === void 0) { stream = false; }
        if (files === void 0) { files = null; }
        var data = {
            inputs: inputs,
            user: user,
            response_mode: stream ? 'streaming' : 'blocking',
            files: files,
        };
        return this.sendRequest(routes$1.runWorkflow.method, routes$1.runWorkflow.url(), data, null, stream);
    };
    return CompletionClientServer;
}(DifyClientServer));
/**
 * Client for interacting with the Dify Chat API
 */
var ChatClientServer = /** @class */ (function (_super) {
    __extends(ChatClientServer, _super);
    function ChatClientServer() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * Create a chat message
     *
     * @param inputs - Input parameters for the chat message
     * @param query - The user's message/query
     * @param user - User identifier
     * @param stream - Whether to request a streaming response
     * @param conversation_id - ID of the conversation to add the message to
     * @param files - Files to include with the message
     * @returns Response from the Dify API
     */
    ChatClientServer.prototype.createChatMessage = function (inputs, query, user, stream, conversation_id, files) {
        if (stream === void 0) { stream = false; }
        if (conversation_id === void 0) { conversation_id = null; }
        if (files === void 0) { files = null; }
        var data = {
            inputs: inputs,
            query: query,
            user: user,
            response_mode: stream ? 'streaming' : 'blocking',
            files: files,
        };
        if (conversation_id)
            data.conversation_id = conversation_id;
        return this.sendRequest(routes$1.createChatMessage.method, routes$1.createChatMessage.url(), data, null, stream);
    };
    /**
     * Get messages from a conversation
     *
     * @param user - User identifier
     * @param conversation_id - ID of the conversation to get messages from
     * @param first_id - ID of the first message to get
     * @param limit - Maximum number of messages to get
     * @returns Chat messages from the Dify API
     */
    ChatClientServer.prototype.getConversationMessages = function (user, conversation_id, first_id, limit) {
        if (conversation_id === void 0) { conversation_id = ''; }
        if (first_id === void 0) { first_id = null; }
        if (limit === void 0) { limit = null; }
        var params = { user: user };
        if (conversation_id)
            params.conversation_id = conversation_id;
        if (first_id)
            params.first_id = first_id;
        if (limit)
            params.limit = limit;
        return this.sendRequest(routes$1.getConversationMessages.method, routes$1.getConversationMessages.url(), null, params);
    };
    /**
     * Get a list of conversations
     *
     * @param user - User identifier
     * @param first_id - ID of the first conversation to get
     * @param limit - Maximum number of conversations to get
     * @param pinned - Whether to only get pinned conversations
     * @returns List of conversations from the Dify API
     */
    ChatClientServer.prototype.getConversations = function (user, first_id, limit, pinned) {
        if (first_id === void 0) { first_id = null; }
        if (limit === void 0) { limit = null; }
        if (pinned === void 0) { pinned = null; }
        var params = { user: user };
        if (first_id)
            params.first_id = first_id;
        if (limit)
            params.limit = limit;
        if (pinned !== null)
            params.pinned = pinned;
        return this.sendRequest(routes$1.getConversations.method, routes$1.getConversations.url(), null, params);
    };
    /**
     * Rename a conversation
     *
     * @param conversation_id - ID of the conversation to rename
     * @param name - New name for the conversation
     * @param user - User identifier
     * @param auto_generate - Whether to auto-generate the name
     * @returns Response from the Dify API
     */
    ChatClientServer.prototype.renameConversation = function (conversation_id, name, user, auto_generate) {
        var data = { name: name, user: user, auto_generate: auto_generate };
        return this.sendRequest(routes$1.renameConversation.method, routes$1.renameConversation.url(conversation_id), data);
    };
    /**
     * Delete a conversation
     *
     * @param conversation_id - ID of the conversation to delete
     * @param user - User identifier
     * @returns Response from the Dify API
     */
    ChatClientServer.prototype.deleteConversation = function (conversation_id, user) {
        var data = { user: user };
        return this.sendRequest(routes$1.deleteConversation.method, routes$1.deleteConversation.url(conversation_id), data);
    };
    return ChatClientServer;
}(DifyClientServer));

/**
 * Web client for interacting with the Dify API from browsers
 * This implementation uses the fetch API and is designed for frontend use
 */
// Routes for the Dify API
var routes = {
    application: {
        method: 'GET',
        url: function () { return '/parameters'; },
    },
    feedback: {
        method: 'POST',
        url: function (message_id) { return "/messages/".concat(message_id, "/feedbacks"); },
    },
    createCompletionMessage: {
        method: 'POST',
        url: function () { return '/completion-messages'; },
    },
    createChatMessage: {
        method: 'POST',
        url: function () { return '/chat-messages'; },
    },
    stopChatMessage: {
        method: 'POST',
        url: function (task_id) { return "/chat-messages/".concat(task_id, "/stop"); },
    },
    getConversationMessages: {
        method: 'GET',
        url: function () { return '/messages'; },
    },
    getConversations: {
        method: 'GET',
        url: function () { return '/conversations'; },
    },
    renameConversation: {
        method: 'POST',
        url: function (conversation_id) { return "/conversations/".concat(conversation_id, "/name"); },
    },
    deleteConversation: {
        method: 'DELETE',
        url: function (conversation_id) { return "/conversations/".concat(conversation_id); },
    },
    fileUpload: {
        method: 'POST',
        url: function () { return '/file-upload'; },
    },
    runWorkflow: {
        method: 'POST',
        url: function () { return '/workflows/run'; },
    },
    audioToText: {
        method: 'POST',
        url: function () { return '/audio-to-text'; },
    },
    datasets: {
        method: 'GET',
        url: function () { return '/datasets'; },
    },
    createDocumentByFile: {
        method: 'POST',
        url: function (dataset_id) { return "/datasets/".concat(dataset_id, "/document/create-by-file"); },
    },
    getDocumentIndexingStatus: {
        method: 'GET',
        url: function (dataset_id, batch) { return "/datasets/".concat(dataset_id, "/documents/").concat(batch, "/indexing-status"); },
    },
    getDatasetDocuments: {
        method: 'GET',
        url: function (dataset_id) { return "/datasets/".concat(dataset_id, "/documents"); },
    },
    getDatasetDocument: {
        method: 'GET',
        url: function (dataset_id, document_id) { return "/datasets/".concat(dataset_id, "/documents/").concat(document_id, "/upload-file"); },
    },
    deleteDatasetDocument: {
        method: 'DELETE',
        url: function (dataset_id, document_id) { return "/datasets/".concat(dataset_id, "/documents/").concat(document_id); },
    },
};
// Singleton instances for clients
var difyClientInstance = null;
var completionClientInstance = null;
var chatClientInstance = null;
/**
 * Base web client for interacting with the Dify API from browsers
 * This implementation is suitable for browser environments where
 * API authentication can be handled by the server
 */
var DifyClientWeb = /** @class */ (function () {
    /**
     * Create a new Dify web client
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     */
    function DifyClientWeb(baseUrl, appKey) {
        if (baseUrl === void 0) { baseUrl = API_PREFIX; }
        if (appKey === void 0) { appKey = 'app1'; }
        this._enableWebSearch = false;
        this.inputs = {};
        this.baseUrl = baseUrl;
        this.appKey = appKey;
        this._enableWebSearch = false;
        this.sessionId = this.getSessionFromStorage();
    }
    Object.defineProperty(DifyClientWeb.prototype, "enableWebSearch", {
        get: function () {
            return this._enableWebSearch;
        },
        set: function (value) {
            this._enableWebSearch = value;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Get the singleton instance of DifyClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    DifyClientWeb.getInstance = function (baseUrl, appKey) {
        if (baseUrl === void 0) { baseUrl = API_PREFIX; }
        if (appKey === void 0) { appKey = 'app1'; }
        if (!difyClientInstance)
            difyClientInstance = new DifyClientWeb(baseUrl, appKey);
        return difyClientInstance;
    };
    /**
     * Set or update the app key
     *
     * @param appKey - The key to identify which application configuration to use
     */
    DifyClientWeb.prototype.setAppKey = function (appKey) {
        this.appKey = appKey;
    };
    /**
     * Set inputs object (overwrites existing inputs)
     *
     * @param inputs - Input parameters to set
     */
    DifyClientWeb.prototype.setInputs = function (inputs) {
        this.inputs = __assign({}, inputs);
    };
    /**
     * set conversion sessionId
     * @param sessionId
     */
    DifyClientWeb.prototype.setSessionId = function (sessionId) {
        this.sessionId = sessionId;
        this.saveSessionToStorage(sessionId);
    };
    /**
     * Add inputs to existing inputs object (merges with existing)
     *
     * @param inputs - Input parameters to add
     */
    DifyClientWeb.prototype.addInputs = function (inputs) {
        this.inputs = __assign(__assign({}, this.inputs), inputs);
    };
    /**
     * Send a request to the Dify API
     *
     * @param method - HTTP method (GET, POST, etc.)
     * @param endpoint - API endpoint
     * @param data - Request payload for POST, PUT requests
     * @param params - Query parameters for the request
     * @param headerParams - Additional headers to include in the request
     * @returns Promise with the response data
     */
    DifyClientWeb.prototype.sendRequest = function (method, endpoint, data, params, headerParams, otherOptions) {
        if (data === void 0) { data = null; }
        if (params === void 0) { params = null; }
        return __awaiter(this, void 0, void 0, function () {
            var options, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        options = {};
                        if (headerParams) {
                            options.headers = __assign({}, headerParams);
                        }
                        // Add app_key to params if provided for GET requests
                        params = params || {};
                        params = __assign({}, params);
                        if (this.appKey)
                            params.app_key = this.appKey;
                        // Add session_id to params as fallback when cookies are not available
                        if (this.sessionId)
                            params.session_id = this.sessionId;
                        // Add app_key to body for non-GET requests
                        if (data && typeof data === 'object') {
                            data = __assign({}, data);
                            if (this.appKey)
                                data.app_key = this.appKey;
                            // Add session_id to body as fallback
                            if (this.sessionId)
                                data.session_id = this.sessionId;
                        }
                        if (!(method === 'GET')) return [3 /*break*/, 2];
                        options.params = params;
                        return [4 /*yield*/, get(endpoint, options, { apiBaseUrl: this.baseUrl })];
                    case 1:
                        response = _a.sent();
                        return [3 /*break*/, 8];
                    case 2:
                        if (!(method === 'POST')) return [3 /*break*/, 4];
                        options.params = params;
                        options.body = data;
                        return [4 /*yield*/, post(endpoint, options, { apiBaseUrl: this.baseUrl })];
                    case 3:
                        response = _a.sent();
                        return [3 /*break*/, 8];
                    case 4:
                        if (!(method === 'DELETE')) return [3 /*break*/, 6];
                        options.body = data;
                        options.params = params;
                        return [4 /*yield*/, del(endpoint, options, { apiBaseUrl: this.baseUrl })];
                    case 5:
                        // console.log('delete', { endpoint, options })
                        response = _a.sent();
                        return [3 /*break*/, 8];
                    case 6:
                        // Fallback to generic request if method not specifically handled
                        options.method = method;
                        if (params)
                            options.params = params;
                        if (data)
                            options.body = data;
                        return [4 /*yield*/, get(endpoint, options, { apiBaseUrl: this.baseUrl })];
                    case 7:
                        response = _a.sent();
                        _a.label = 8;
                    case 8:
                        // Handle session from response
                        this.handleSessionFromResponse(response);
                        return [2 /*return*/, response];
                }
            });
        });
    };
    /**
     * Get application parameters
     *
     * @returns Application parameters from the Dify API
     */
    DifyClientWeb.prototype.getApplicationParameters = function () {
        return this.sendRequest(routes.application.method, routes.application.url());
    };
    /**
     * Send feedback for a message
     *
     * @param message_id - ID of the message to provide feedback for
     * @param rating - Feedback rating ('like' or 'dislike')
     * @param user_id - Optional user identifier
     * @returns Response from the Dify API
     */
    DifyClientWeb.prototype.messageFeedback = function (message_id, rating, user_id) {
        var data = { rating: rating };
        if (user_id)
            data.user_id = user_id;
        return this.sendRequest(routes.feedback.method, routes.feedback.url(message_id), data);
    };
    /**
     * Upload a file to the Dify API
     *
     * @param file - File to upload
     * @returns Promise that resolves with the uploaded file ID
     */
    DifyClientWeb.prototype.fileUpload = function (file) {
        return __awaiter(this, void 0, void 0, function () {
            var formData, url;
            return __generator(this, function (_a) {
                formData = new FormData();
                formData.append('file', file);
                url = "".concat(routes.fileUpload.url(), "?app_key=").concat(this.appKey);
                console.log(url);
                return [2 /*return*/, postFormData(url, formData, { apiBaseUrl: this.baseUrl })];
            });
        });
    };
    /**
     * Convert audio to text
     *
     * @param audioFile - Audio file to transcribe
     * @param audioType - MIME type of the audio file
     * @returns Transcription response from the Dify API
     */
    DifyClientWeb.prototype.audioToText = function (audioFile, audioType) {
        if (audioType === void 0) { audioType = 'audio/mp3'; }
        return __awaiter(this, void 0, void 0, function () {
            var formData;
            return __generator(this, function (_a) {
                formData = new FormData();
                formData.append('file', audioFile);
                formData.append('type', audioType);
                // Add app_key to formData if provided
                if (this.appKey)
                    formData.append('app_key', this.appKey);
                return [2 /*return*/, postFormData("".concat(routes.audioToText.url(), "?app_key=").concat(this.appKey), formData, { apiBaseUrl: this.baseUrl })];
            });
        });
    };
    DifyClientWeb.prototype.getDatasets = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.sendRequest(routes.datasets.method, routes.datasets.url())];
            });
        });
    };
    DifyClientWeb.prototype.createDocumentByFile = function (dataset_id, data) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.sendRequest(routes.createDocumentByFile.method, routes.createDocumentByFile.url(dataset_id), data)];
            });
        });
    };
    DifyClientWeb.prototype.getDocumentIndexingStatus = function (dataset_id, batch) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.sendRequest(routes.getDocumentIndexingStatus.method, routes.getDocumentIndexingStatus.url(dataset_id, batch))];
            });
        });
    };
    DifyClientWeb.prototype.getDatasetDocuments = function (dataset_id, keyword, page, limit) {
        if (keyword === void 0) { keyword = ''; }
        if (page === void 0) { page = 1; }
        if (limit === void 0) { limit = 20; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.sendRequest(routes.getDatasetDocuments.method, routes.getDatasetDocuments.url(dataset_id), undefined, {
                        keyword: keyword,
                        page: page,
                        limit: limit,
                    })];
            });
        });
    };
    DifyClientWeb.prototype.deleteDatasetDocument = function (dataset_id, document_id) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.sendRequest(routes.deleteDatasetDocument.method, routes.deleteDatasetDocument.url(dataset_id, document_id))];
            });
        });
    };
    DifyClientWeb.prototype.getDatasetDocument = function (dataset_id, document_id) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.sendRequest(routes.getDatasetDocument.method, routes.getDatasetDocument.url(dataset_id, document_id))];
            });
        });
    };
    /**
     * Get session ID from localStorage as fallback when cookies fail
     */
    DifyClientWeb.prototype.getSessionFromStorage = function () {
        if (typeof window !== 'undefined' && window.localStorage) {
            try {
                return localStorage.getItem('dify_session_id') || undefined;
            }
            catch (e) {
                // localStorage might not be available
                return undefined;
            }
        }
        return undefined;
    };
    /**
     * Save session ID to localStorage as fallback
     */
    DifyClientWeb.prototype.saveSessionToStorage = function (sessionId) {
        if (typeof window !== 'undefined' && window.localStorage) {
            try {
                localStorage.setItem('dify_session_id', sessionId);
                localStorage.setItem("dify_session_".concat(this.appKey), sessionId);
            }
            catch (e) {
                // localStorage might not be available, ignore
            }
        }
    };
    /**
     * Extract session ID from API response and save it
     */
    DifyClientWeb.prototype.handleSessionFromResponse = function (response) {
        if (response && response.session_id) {
            this.sessionId = response.session_id;
            this.saveSessionToStorage(response.session_id);
        }
    };
    return DifyClientWeb;
}());
/**
 * Completion client for web browsers
 */
var CompletionClientWeb = /** @class */ (function (_super) {
    __extends(CompletionClientWeb, _super);
    function CompletionClientWeb() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * Get the singleton instance of CompletionClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    CompletionClientWeb.getInstance = function (baseUrl, appKey) {
        if (baseUrl === void 0) { baseUrl = API_PREFIX; }
        if (appKey === void 0) { appKey = 'app1'; }
        if (!completionClientInstance)
            completionClientInstance = new CompletionClientWeb(baseUrl, appKey);
        return completionClientInstance;
    };
    /**
     * Create a completion message
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param files - Files to include with the request
     * @returns Promise with completion response
     */
    CompletionClientWeb.prototype.createCompletionMessage = function (inputs, user, files) {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                data = {
                    inputs: __assign(__assign({}, this.inputs), inputs),
                };
                if (user)
                    data.user = user;
                if (files && files.length > 0)
                    data.files = files;
                return [2 /*return*/, this.sendRequest(routes.createCompletionMessage.method, routes.createCompletionMessage.url(), data)];
            });
        });
    };
    /**
     * Run a workflow
     *
     * @param inputs - Input parameters for the workflow
     * @param user - User identifier
     * @param files - Files to include with the request
     * @returns Promise with workflow response
     */
    CompletionClientWeb.prototype.runWorkflow = function (inputs, files) {
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                data = {
                    inputs: __assign(__assign({}, this.inputs), inputs),
                };
                if (files && files.length > 0)
                    data.files = files;
                return [2 /*return*/, this.sendRequest(routes.runWorkflow.method, routes.runWorkflow.url(), data)];
            });
        });
    };
    /**
     * Create a streaming completion message
     * This uses the ssePost method from base.ts to handle streaming
     *
     * @param inputs - Input parameters for the completion
     * @param user - User identifier
     * @param files - Files to include with the request
     * @param callbacks - Callbacks for streaming events
     * @returns void
     */
    CompletionClientWeb.prototype.streamCompletionMessage = function (inputs, user, files, callbacks) {
        var data = {
            inputs: __assign(__assign({}, this.inputs), inputs),
            response_mode: 'streaming',
        };
        if (user)
            data.user = user;
        if (files && files.length > 0)
            data.files = files;
        // Add app_key to data if provided
        if (this.appKey)
            data.app_key = this.appKey;
        // Add session_id to data as fallback
        if (this.sessionId)
            data.session_id = this.sessionId;
        var options = __assign({ apiBaseUrl: this.baseUrl }, callbacks);
        ssePost(routes.createCompletionMessage.url(), { body: data }, options);
    };
    return CompletionClientWeb;
}(DifyClientWeb));
/**
 * Chat client for web browsers
 */
var ChatClientWeb = /** @class */ (function (_super) {
    __extends(ChatClientWeb, _super);
    function ChatClientWeb() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.message_files = [];
        return _this;
    }
    /**
     * Get the singleton instance of ChatClientWeb
     *
     * @param baseUrl - The custom API base URL. Defaults to "/api"
     * @param appKey - The key to identify which application configuration to use
     * @returns The singleton instance
     */
    ChatClientWeb.getInstance = function (baseUrl, appKey) {
        if (baseUrl === void 0) { baseUrl = API_PREFIX; }
        if (appKey === void 0) { appKey = 'app1'; }
        if (!chatClientInstance)
            chatClientInstance = new ChatClientWeb(baseUrl, appKey);
        return chatClientInstance;
    };
    /**
     * Create a chat message
     *
     * @param inputs - Input parameters for the chat
     * @param query - The user's message/query
     * @param conversation_id - ID of the conversation to add the message to
     * @param user - User identifier
     * @param files - Files to include with the message
     * @returns Promise with chat message response
     */
    ChatClientWeb.prototype.createChatMessage = function (inputs, query, response_mode, conversation_id, user, files) {
        if (response_mode === void 0) { response_mode = 'streaming'; }
        return __awaiter(this, void 0, void 0, function () {
            var data;
            return __generator(this, function (_a) {
                if (this.enableWebSearch) {
                    if (!inputs)
                        inputs = {};
                    inputs.web_search = true;
                }
                data = {
                    inputs: __assign(__assign({}, this.inputs), inputs),
                    query: query,
                    response_mode: response_mode,
                };
                if (conversation_id)
                    data.conversation_id = conversation_id;
                if (user)
                    data.user = user;
                if (this.sessionId)
                    data.session_id = this.sessionId;
                if (files && files.length > 0)
                    data.files = files;
                return [2 /*return*/, fetch("".concat(this.baseUrl).concat(routes.createChatMessage.url(), "?app_key=").concat(this.appKey), {
                        method: routes.createChatMessage.method,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data),
                        credentials: 'include', // Include cookies for session management
                    })];
            });
        });
    };
    /**
     * Create a streaming chat message
     * This uses ssePost from base.ts to handle streaming
     *
     * @param inputs - Input parameters for the chat
     * @param query - The user's message/query
     * @param conversation_id - ID of the conversation to add the message to
     * @param user - User identifier
     * @param files - Files to include with the message
     * @param callbacks - Callbacks for streaming events
     * @returns void
     */
    ChatClientWeb.prototype.streamChatMessage = function (_a, callbacks) {
        var inputs = _a.inputs, query = _a.query, conversation_id = _a.conversation_id, user = _a.user, files = _a.files;
        // console.trace('streamChatMessage', { inputs, query, conversation_id, user, files })
        if (this.enableWebSearch) {
            if (!inputs)
                inputs = {};
            inputs.web_search = true;
        }
        var data = {
            inputs: __assign(__assign({}, this.inputs), inputs),
            query: query,
            response_mode: 'streaming',
        };
        if (conversation_id)
            data.conversation_id = conversation_id;
        if (user)
            data.user = user;
        if (files && files.length > 0)
            data.files = files;
        if (this.message_files && this.message_files.length > 0) {
            var sendFileAry = this.message_files.map(function (item) {
                return {
                    type: item.type,
                    transfer_method: item.transfer_method,
                    url: '',
                    upload_file_id: item.id,
                };
            });
            console.log(sendFileAry);
            // data.files = [...sendFileAry]
        }
        // Add app_key to data if provided
        if (this.appKey)
            data.app_key = this.appKey;
        // Add session_id to data as fallback
        if (this.sessionId)
            data.session_id = this.sessionId;
        // console.log('streamChatMessage', { url: routes.createChatMessage.url(), data })
        var options = __assign({ apiBaseUrl: this.baseUrl }, callbacks);
        ssePost(routes.createChatMessage.url(), { body: data }, options);
    };
    /**
     * Get messages from a conversation
     *
     * @param conversation_id - ID of the conversation to get messages from
     * @param first_id - ID of the first message to get
     * @param limit - Maximum number of messages to get
     * @returns Promise with conversation messages
     */
    ChatClientWeb.prototype.getConversationMessages = function (conversation_id, first_id, limit) {
        return __awaiter(this, void 0, void 0, function () {
            var params, result;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        params = { conversation_id: conversation_id };
                        this.message_files = [];
                        if (first_id)
                            params.first_id = first_id;
                        if (limit)
                            params.limit = limit;
                        return [4 /*yield*/, this.sendRequest(routes.getConversationMessages.method, routes.getConversationMessages.url(), null, params)];
                    case 1:
                        result = _a.sent();
                        console.log(' result --- ', result);
                        result.data.map(function (item) {
                            var _a;
                            if (item.message_files && item.message_files.length > 0)
                                (_a = _this.message_files).push.apply(_a, item.message_files);
                            return undefined;
                        });
                        console.log(' conversation msg --- ', result);
                        return [2 /*return*/, result];
                }
            });
        });
    };
    /**
     * Get a list of conversations
     *
     * @param first_id - ID of the first conversation to get
     * @param limit - Maximum number of conversations to get
     * @param pinned - Whether to only get pinned conversations
     * @returns Promise with list of conversations
     */
    ChatClientWeb.prototype.getConversations = function (first_id, limit, pinned) {
        var params = {};
        if (first_id)
            params.first_id = first_id;
        if (limit)
            params.limit = limit;
        if (pinned !== undefined)
            params.pinned = pinned;
        return this.sendRequest(routes.getConversations.method, routes.getConversations.url(), null, params);
    };
    /**
     * Rename a conversation
     *
     * @param conversation_id - ID of the conversation to rename
     * @param name - New name for the conversation
     * @param auto_generate - Whether to auto-generate the name
     * @returns Promise with rename response
     */
    ChatClientWeb.prototype.renameConversation = function (conversation_id, name, auto_generate) {
        if (auto_generate === void 0) { auto_generate = false; }
        return this.sendRequest(routes.renameConversation.method, routes.renameConversation.url(conversation_id), { name: name, auto_generate: auto_generate });
    };
    /**
     * Delete a conversation
     *
     * @param conversation_id - ID of the conversation to delete
     * @returns Promise with delete response
     */
    ChatClientWeb.prototype.deleteConversation = function (conversation_id) {
        return this.sendRequest(routes.deleteConversation.method, routes.deleteConversation.url(conversation_id));
    };
    /**
     * Stop a chat message task
     *
     * @param task_id - ID of the task to stop
     * @param user - User identifier
     * @returns Promise with stop response
     */
    ChatClientWeb.prototype.stopChatMessage = function (task_id, user) {
        var data = {};
        if (user)
            data.user = user;
        return this.sendRequest(routes.stopChatMessage.method, routes.stopChatMessage.url(task_id), data);
    };
    return ChatClientWeb;
}(DifyClientWeb));

/**
 * Send a chat message with streaming response
 *
 * @param body - Request body with inputs, query, and optional conversation ID
 * @param callbacks - Callback functions for different events
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the request is complete
 */
var sendChatMessage = function (body, _a, apiBaseUrl) {
    var onData = _a.onData, onCompleted = _a.onCompleted, onThought = _a.onThought, onFile = _a.onFile, onError = _a.onError, getAbortController = _a.getAbortController, onMessageEnd = _a.onMessageEnd, onMessageReplace = _a.onMessageReplace, onWorkflowStarted = _a.onWorkflowStarted, onNodeStarted = _a.onNodeStarted, onNodeFinished = _a.onNodeFinished, onWorkflowFinished = _a.onWorkflowFinished;
    return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_b) {
            return [2 /*return*/, ssePost('chat-messages', {
                    body: __assign(__assign({}, body), { response_mode: 'streaming' }),
                }, {
                    onData: onData,
                    onCompleted: onCompleted,
                    onThought: onThought,
                    onFile: onFile,
                    onError: onError,
                    getAbortController: getAbortController,
                    onMessageEnd: onMessageEnd,
                    onMessageReplace: onMessageReplace,
                    onNodeStarted: onNodeStarted,
                    onWorkflowStarted: onWorkflowStarted,
                    onWorkflowFinished: onWorkflowFinished,
                    onNodeFinished: onNodeFinished,
                    apiBaseUrl: apiBaseUrl,
                })];
        });
    });
};
/**
 * Helper function to handle streaming response from the Dify API
 * For use with the streamChatMessage and streamCompletionMessage methods
 * of the Web clients
 *
 * @param response - The Response object from fetch
 * @param callbacks - Callback functions for handling different events
 */
var handleStreamingResponse = function (response, _a) {
    var _b;
    var onData = _a.onData, onCompleted = _a.onCompleted, onThought = _a.onThought, onFile = _a.onFile, onError = _a.onError, onMessageEnd = _a.onMessageEnd, onMessageReplace = _a.onMessageReplace, onWorkflowStarted = _a.onWorkflowStarted, onNodeStarted = _a.onNodeStarted, onNodeFinished = _a.onNodeFinished, onWorkflowFinished = _a.onWorkflowFinished;
    if (!response.ok) {
        onError === null || onError === void 0 ? void 0 : onError("Stream request failed with status ".concat(response.status));
        return;
    }
    var reader = (_b = response.body) === null || _b === void 0 ? void 0 : _b.getReader();
    var decoder = new TextDecoder('utf-8');
    var buffer = '';
    var bufferObj;
    var isFirstMessage = true;
    function read() {
        var hasError = false;
        reader === null || reader === void 0 ? void 0 : reader.read().then(function (result) {
            if (result.done) {
                onCompleted && onCompleted();
                return;
            }
            buffer += decoder.decode(result.value, { stream: true });
            var lines = buffer.split('\n');
            try {
                lines.forEach(function (message) {
                    if (message.startsWith('data: ')) {
                        try {
                            bufferObj = JSON.parse(message.substring(6));
                        }
                        catch (e) {
                            // Handle message cut off
                            onData === null || onData === void 0 ? void 0 : onData('', isFirstMessage, {
                                conversationId: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.conversation_id,
                                messageId: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.message_id,
                            });
                            return;
                        }
                        if (bufferObj.status === 400 || !bufferObj.event) {
                            onData === null || onData === void 0 ? void 0 : onData('', false, {
                                conversationId: undefined,
                                messageId: '',
                                errorMessage: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.message,
                                errorCode: bufferObj === null || bufferObj === void 0 ? void 0 : bufferObj.code,
                            });
                            hasError = true;
                            onCompleted === null || onCompleted === void 0 ? void 0 : onCompleted(true);
                            return;
                        }
                        if (bufferObj.event === 'message' || bufferObj.event === 'agent_message') {
                            onData === null || onData === void 0 ? void 0 : onData(bufferObj.answer, isFirstMessage, {
                                conversationId: bufferObj.conversation_id,
                                taskId: bufferObj.task_id,
                                messageId: bufferObj.id,
                            });
                            isFirstMessage = false;
                        }
                        else if (bufferObj.event === 'agent_thought') {
                            // @ts-expect-error
                            onThought === null || onThought === void 0 ? void 0 : onThought(bufferObj);
                        }
                        else if (bufferObj.event === 'message_file') {
                            // @ts-expect-error
                            onFile === null || onFile === void 0 ? void 0 : onFile(bufferObj);
                        }
                        else if (bufferObj.event === 'message_end') {
                            // @ts-expect-error
                            onMessageEnd === null || onMessageEnd === void 0 ? void 0 : onMessageEnd(bufferObj);
                        }
                        else if (bufferObj.event === 'message_replace') {
                            // @ts-expect-error
                            onMessageReplace === null || onMessageReplace === void 0 ? void 0 : onMessageReplace(bufferObj);
                        }
                        else if (bufferObj.event === 'workflow_started') {
                            // @ts-expect-error
                            onWorkflowStarted === null || onWorkflowStarted === void 0 ? void 0 : onWorkflowStarted(bufferObj);
                        }
                        else if (bufferObj.event === 'workflow_finished') {
                            // @ts-expect-error
                            onWorkflowFinished === null || onWorkflowFinished === void 0 ? void 0 : onWorkflowFinished(bufferObj);
                        }
                        else if (bufferObj.event === 'node_started') {
                            // @ts-expect-error
                            onNodeStarted === null || onNodeStarted === void 0 ? void 0 : onNodeStarted(bufferObj);
                        }
                        else if (bufferObj.event === 'node_finished') {
                            // @ts-expect-error
                            onNodeFinished === null || onNodeFinished === void 0 ? void 0 : onNodeFinished(bufferObj);
                        }
                    }
                });
                buffer = lines[lines.length - 1];
            }
            catch (e) {
                onData === null || onData === void 0 ? void 0 : onData('', false, {
                    conversationId: undefined,
                    messageId: '',
                    errorMessage: "".concat(e),
                });
                hasError = true;
                onCompleted === null || onCompleted === void 0 ? void 0 : onCompleted(true);
                return;
            }
            if (!hasError)
                read();
        }).catch(function (e) {
            onError === null || onError === void 0 ? void 0 : onError("".concat(e));
            onCompleted === null || onCompleted === void 0 ? void 0 : onCompleted(true);
        });
    }
    read();
};
/**
 * Fetch conversations for a user
 *
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with conversations data
 */
var fetchConversations = function (apiBaseUrl) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, get('conversations', { params: { limit: 100, first_id: '' } }, { apiBaseUrl: apiBaseUrl })];
    });
}); };
/**
 * Fetch chat messages for a conversation
 *
 * @param conversationId - ID of the conversation to fetch messages for
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with chat messages data
 */
var fetchChatList = function (conversationId, apiBaseUrl) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, get('messages', { params: { conversation_id: conversationId, limit: 20, last_id: '' } }, { apiBaseUrl: apiBaseUrl })];
    });
}); };
/**
 * Delete a conversation
 *
 * @param conversationId - ID of the conversation to delete
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the conversation is deleted
 */
var deleteConversation = function (conversationId, apiBaseUrl) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, del("conversations/".concat(conversationId), {}, { apiBaseUrl: apiBaseUrl })];
    });
}); };
/**
 * Fetch application parameters
 *
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with application parameters
 */
var fetchAppParams = function (apiBaseUrl) { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        return [2 /*return*/, get('parameters', {}, { apiBaseUrl: apiBaseUrl })];
    });
}); };
/**
 * Update feedback for a message
 *
 * @param url - URL of the feedback endpoint
 * @param body - Feedback data
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the feedback is updated
 */
var updateFeedback = function (_a, apiBaseUrl) {
    var url = _a.url, body = _a.body;
    return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_b) {
            return [2 /*return*/, post(url, { body: body }, { apiBaseUrl: apiBaseUrl })];
        });
    });
};
/**
 * Generate or rename a conversation
 *
 * @param id - ID of the conversation to rename
 * @param name - New name for the conversation (optional)
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves when the conversation is renamed
 */
var generationConversationName = function (id, name, apiBaseUrl) {
    if (name === void 0) { name = ''; }
    return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            return [2 /*return*/, post("conversations/".concat(id, "/name"), { body: { auto_generate: true, name: name } }, { apiBaseUrl: apiBaseUrl })];
        });
    });
};
/**
 * Convert audio to text
 *
 * @param audioFile - Audio file to transcribe
 * @param audioType - MIME type of the audio file
 * @param apiBaseUrl - Optional custom API base URL
 * @returns Promise that resolves with transcription data
 */
var audioToText = function (audioFile, audioType, apiBaseUrl) {
    if (audioType === void 0) { audioType = 'audio/mp3'; }
    return __awaiter(void 0, void 0, void 0, function () {
        var formData;
        return __generator(this, function (_a) {
            formData = new FormData();
            formData.append('file', audioFile);
            formData.append('type', audioType);
            return [2 /*return*/, postFormData('audio-to-text', formData, { apiBaseUrl: apiBaseUrl })];
        });
    });
};

exports.API_PREFIX = API_PREFIX;
exports.BASE_URL = BASE_URL;
exports.ChatClientServer = ChatClientServer;
exports.ChatClientWeb = ChatClientWeb;
exports.CompletionClientServer = CompletionClientServer;
exports.CompletionClientWeb = CompletionClientWeb;
exports.DifyChatbot = DifyChatbot;
exports.DifyChatboxComunication = DifyChatboxComunication;
exports.DifyClientServer = DifyClientServer;
exports.DifyClientWeb = DifyClientWeb;
exports.audioToText = audioToText;
exports.del = del;
exports.deleteConversation = deleteConversation;
exports.fetchAppParams = fetchAppParams;
exports.fetchChatList = fetchChatList;
exports.fetchConversations = fetchConversations;
exports.generationConversationName = generationConversationName;
exports.get = get;
exports.handleStreamingResponse = handleStreamingResponse;
exports.post = post;
exports.postFormData = postFormData;
exports.put = put;
exports.request = request;
exports.routes = routes$1;
exports.sendChatMessage = sendChatMessage;
exports.ssePost = ssePost;
exports.updateFeedback = updateFeedback;
exports.upload = upload;
exports.webRoutes = routes;
